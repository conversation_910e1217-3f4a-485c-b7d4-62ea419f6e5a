// public/scripts/common_module.js

// Função para aguardar o sistema de persistência estar disponível
// public/scripts/common_module.js

// Importa a função waitForApiSystem do sistema de persistência global
// import { waitForApiSystem } from './api_persistence.js'; // Removido, agora acessa via window

// Store de dados do transformador usando o novo sistema
const transformerDataStore = {
    async getData() {
        console.log('[common_module] transformerDataStore.getData: Iniciando');
        try {
            // Acessa a função globalmente exposta
            const apiSystem = await window.waitForApiSystem();
            if (apiSystem) {
                console.log('[common_module] transformerDataStore.getData: Usando apiDataSystem');
                const store = apiSystem.getStore('transformerInputs');
                const data = await store.getData();
                console.log('[common_module] transformerDataStore.getData: Dados obtidos via apiDataSystem', data);
                return data;
            } else {
                console.log('[common_module] transformerDataStore.getData: Usando fallback localStorage');
                // Fallback para localStorage
                const data = JSON.parse(localStorage.getItem('transformerInputsData')) || {};
                console.log('[common_module] transformerDataStore.getData: Dados obtidos via localStorage', data);
                return data;
            }
        } catch (error) {
            console.error('[common_module] transformerDataStore.getData: Erro ao obter dados:', error);
            return {};
        } finally {
            console.log('[common_module] transformerDataStore.getData: Concluído');
        }
    },

    async setData(newData) {
        console.log('[common_module] transformerDataStore.setData: Iniciando com dados:', newData);
        try {
            // Acessa a função globalmente exposta
            const apiSystem = await window.waitForApiSystem();
            if (apiSystem) {
                console.log('[common_module] transformerDataStore.setData: Usando apiDataSystem');
                const store = apiSystem.getStore('transformerInputs');
                await store.updateData(newData);
                console.log("[common_module] transformerDataStore.setData: Dados atualizados via apiDataSystem");
            } else {
                console.log('[common_module] transformerDataStore.setData: Usando fallback localStorage');
                // Fallback para localStorage
                localStorage.setItem('transformerInputsData', JSON.stringify(newData));
                console.log("[common_module] transformerDataStore.setData: Dados atualizados via localStorage (fallback)");
            }
        } catch (error) {
            console.error('[common_module] transformerDataStore.setData: Erro ao salvar dados:', error);
        } finally {
            console.log('[common_module] transformerDataStore.setData: Concluído');
        }
    }
};

// Funções para preencher o transformer_info_panel
async function loadAndPopulateTransformerInfo(targetElementId) {
    console.log(`[common_module] loadAndPopulateTransformerInfo: Iniciando para ${targetElementId}`);

    // Se o targetElementId for da página de transformer_inputs, não carregue o painel.
    // O painel de informações do transformador não deve ser exibido nesta página.
    if (targetElementId === 'transformer-info-transformer_inputs-page') {
        const targetElement = document.getElementById(targetElementId);
        if (targetElement) {
            targetElement.innerHTML = ''; // Garante que o elemento esteja vazio
            targetElement.classList.add('d-none'); // Oculta o elemento se ele existir
        }
        console.log(`[common_module] loadAndPopulateTransformerInfo: Painel de informações do transformador oculto para ${targetElementId}`);
        return;
    }

    const targetElement = document.getElementById(targetElementId);
    if (!targetElement) {
        console.error(`[common_module] loadAndPopulateTransformerInfo: Elemento alvo para info do transformador não encontrado: ${targetElementId}`);
        console.log(`[common_module] loadAndPopulateTransformerInfo: Concluído (elemento não encontrado)`);
        return;
    }

    try {
        console.log('[common_module] loadAndPopulateTransformerInfo: Carregando template HTML');
        // Carrega o template HTML do painel de informações
        const response = await fetch('templates/transformer_info_panel.html'); // Caminho relativo ao index.html
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} ao carregar template.`);
        }
        const templateHtml = await response.text();
        targetElement.innerHTML = templateHtml;
        console.log('[common_module] loadAndPopulateTransformerInfo: Template HTML carregado e inserido');

        console.log('📊 [FLUXO] loadAndPopulateTransformerInfo: Buscando dados do transformador');

        // REMOVIDO - limpeza forçada de cache estava causando perda dos dados básicos
        // try {
        //     const apiSystem = await window.waitForApiSystem();
        //     if (apiSystem) {
        //         const store = apiSystem.getStore('transformerInputs');
        //         if (store && store.clearCache) {
        //             store.clearCache();
        //             console.log('🧹 [FLUXO] TEMPLATE: Cache do transformerInputs limpo antes de carregar template');
        //         }
        //     }
        // } catch (error) {
        //     console.warn('⚠️ [FLUXO] TEMPLATE: Erro ao limpar cache:', error);
        // }

        // Busca os dados básicos do transformador via apiDataSystem
        const transformerData = await transformerDataStore.getData();
        console.log('📊 [FLUXO] loadAndPopulateTransformerInfo: Dados do transformador obtidos:', transformerData);

        // Extrai dados do formato correto (pode estar em formData ou no nível raiz)
        let basicData = null;
        if (transformerData) {
            // Tenta diferentes estruturas de dados com prioridade para formData
            if (transformerData.formData && Object.keys(transformerData.formData).length > 0) {
                basicData = transformerData.formData;
                console.log('📊 [FLUXO] TEMPLATE: Usando dados de formData:', Object.keys(basicData).length, 'campos');
            } else if (transformerData.inputs && transformerData.inputs.dados_basicos) {
                basicData = transformerData.inputs.dados_basicos;
                console.log('📊 [FLUXO] TEMPLATE: Usando dados de inputs.dados_basicos:', Object.keys(basicData).length, 'campos');
            } else if (transformerData.data) {
                basicData = transformerData.data;
                console.log('📊 [FLUXO] TEMPLATE: Usando dados de data:', Object.keys(basicData).length, 'campos');
            } else {
                basicData = transformerData;
                console.log('📊 [FLUXO] TEMPLATE: Usando dados diretos:', Object.keys(basicData).length, 'campos');
            }
        }

        // Função auxiliar para preencher campo com verificação de existência e adicionar unidade
        const fillField = (elementId, value, unit = '') => {
            const element = document.getElementById(elementId);
            if (element) {
                // Verifica se o valor é um número e formata para 2 casas decimais, caso contrário usa '-'
                if (typeof value === 'number' && !isNaN(value)) {
                    element.textContent = `${value.toFixed(2)} ${unit}`.trim();
                } else {
                    element.textContent = `${value || '-'} ${unit}`.trim();
                }
            }
        };
        // Verifica se há dados VÁLIDOS (não apenas null)
        const hasValidData = basicData && Object.keys(basicData).length > 0 &&
                           Object.values(basicData).some(value =>
                               value !== null && value !== undefined && value !== ''
                           );

        // Preenche os campos do template APENAS se houver dados válidos
        if (hasValidData) {
            console.log("✅ [FLUXO] TEMPLATE: Preenchendo painel com dados:", basicData);
            console.log("🔍 [FLUXO] TEMPLATE: Tipo:", basicData.tipo_transformador, "Norma:", basicData.norma_iso);
            console.log("🔍 [FLUXO] TEMPLATE: Total de campos:", Object.keys(basicData).length);

            // Destaca dados importantes para cada módulo específico
            if (targetElementId.includes('losses')) {
                highlightInheritedDataForLosses(basicData);
            } else if (targetElementId.includes('short_circuit')) {
                highlightInheritedDataForShortCircuit(basicData);
            } else if (targetElementId.includes('temperature_rise')) {
                highlightInheritedDataForTemperatureRise(basicData);
            } else if (targetElementId.includes('induced_voltage')) {
                highlightInheritedDataForInducedVoltage(basicData);
            } else if (targetElementId.includes('applied_voltage')) {
                highlightInheritedDataForAppliedVoltage(basicData);
            }

            // Especificações Gerais
            // Especificações Gerais
            fillField('info-potencia-mva', basicData.potencia_mva, 'MVA');
            fillField('info-frequencia', basicData.frequencia, 'Hz');

            // Mapear o valor do dropdown para o texto de exibição completo
            let tipoParaExibicao = basicData.tipo_transformador;

            // Mapear os valores do dropdown para os textos completos
            const tipoMapping = {
                "Trifásico": "Transformador Trifásico",
                "Monofásico": "Transformador Monofásico",
                "Autotransformador": "Autotransformador Trifásico",
                "Autotransformador Monofásico": "Autotransformador Monofásico"
            };

            if (tipoMapping[basicData.tipo_transformador]) {
                tipoParaExibicao = tipoMapping[basicData.tipo_transformador];
            }

            fillField('info-tipo-transformador', tipoParaExibicao);
            fillField('info-grupo-ligacao', basicData.grupo_ligacao);
            fillField('info-liquido-isolante', basicData.liquido_isolante);
            fillField('info-norma-iso', basicData.norma_iso);
            // Temperaturas e Pesos
            fillField('info-elevacao-oleo-topo', basicData.elevacao_oleo_topo, '°C');
            fillField('info-elevacao-enrol', basicData.elevacao_enrol, '°C');
            fillField('info-peso-parte-ativa', basicData.peso_parte_ativa, 'ton');
            fillField('info-peso-tanque', basicData.peso_tanque_acessorios, 'ton'); // Mapped to peso_tanque_acessorios
            fillField('info-peso-oleo', basicData.peso_oleo, 'ton');
            fillField('info-peso-total', basicData.peso_total, 'ton');
            fillField('info-tipo-isolamento', basicData.tipo_isolamento);

            // Dados da Alta Tensão (AT)
            fillField('info-tensao-at', basicData.tensao_at, 'kV');
            fillField('info-classe-tensao-at', basicData.classe_tensao_at, 'kV');
            fillField('info-corrente-nominal-at', basicData.corrente_nominal_at, 'A');
            fillField('info-impedancia', basicData.impedancia, '%');
            fillField('info-nbi-at', basicData.nbi_at, 'kVp');
            fillField('info-sil-im-at', basicData.sil_at, 'kVp'); // Adicionado SIL/IM AT
            fillField('info-conexao-at', basicData.conexao_at);
            fillField('info-conexao-neutro-at', basicData.conexao_neutro_at); // Adicionado Conexao Neutro AT
            fillField('info-tensao-neutro-at', basicData.tensao_bucha_neutro_at, 'kVp'); // Adicionado Tensao Neutro AT
            fillField('info-corrente-neutro-at', basicData.corrente_neutro_at, 'A'); // Adicionado Corrente Neutro AT
            fillField('info-nbi-neutro-at', basicData.nbi_neutro_at, 'kVp'); // Adicionado NBI Neutro AT
            fillField('info-sil-im-neutro-at', basicData.sil_neutro_at, 'kVp'); // Adicionado SIL/IM Neutro AT
            fillField('info-iac-at', basicData.iac_at, 'kVp'); // Alterado para kVp conforme solicitado

            // TAPs AT
            fillField('info-tensao-at-tap-maior', basicData.tensao_at_tap_maior, 'kV');
            fillField('info-tensao-at-tap-menor', basicData.tensao_at_tap_menor, 'kV');
            fillField('info-corrente-nominal-at-tap-maior', basicData.corrente_nominal_at_tap_maior, 'A');
            fillField('info-corrente-nominal-at-tap-menor', basicData.corrente_nominal_at_tap_menor, 'A');
            fillField('info-impedancia-tap-maior', basicData.impedancia_tap_maior, '%');
            fillField('info-impedancia-tap-menor', basicData.impedancia_tap_menor, '%');
            fillField('info-classe-tensao-at-tap-maior', basicData.classe_tensao_at_tap_maior, 'kV');
            fillField('info-classe-tensao-at-tap-menor', basicData.classe_tensao_at_tap_menor, 'kV');
            fillField('info-conexao-at-tap-maior', basicData.conexao_at_tap_maior);
            fillField('info-conexao-at-tap-menor', basicData.conexao_at_tap_menor);
            fillField('info-nbi-at-tap-maior', basicData.nbi_at_tap_maior, 'kVp');
            fillField('info-nbi-at-tap-menor', basicData.nbi_at_tap_menor, 'kVp');
            fillField('info-sil-im-at-tap-maior', basicData.sil_at_tap_maior, 'kVp');
            fillField('info-sil-im-at-tap-menor', basicData.sil_at_tap_menor, 'kVp');
            fillField('info-iac-at-tap-maior', basicData.iac_at_tap_maior, 'kVp');
            fillField('info-iac-at-tap-menor', basicData.iac_at_tap_menor, 'kVp');
            fillField('info-degrau-comutador', basicData.degrau_comutador);
            fillField('info-num-degraus-comutador', basicData.num_degraus_comutador);
            fillField('info-posicao-comutador', basicData.posicao_comutador);
            
            // Dados da Baixa Tensão (BT)
            fillField('info-tensao-bt', basicData.tensao_bt, 'kV');
            fillField('info-classe-tensao-bt', basicData.classe_tensao_bt, 'kV');
            fillField('info-corrente-nominal-bt', basicData.corrente_nominal_bt, 'A');
            fillField('info-nbi-bt', basicData.nbi_bt, 'kVp');
            fillField('info-sil-im-bt', basicData.sil_bt, 'kVp'); // Adicionado SIL/IM BT
            fillField('info-conexao-bt', basicData.conexao_bt);
            fillField('info-classe-neutro-bt', basicData.tensao_bucha_neutro_bt, 'kVp'); // Adicionado Classe Neutro BT
            fillField('info-nbi-neutro-bt', basicData.nbi_neutro_bt, 'kVp'); // Adicionado NBI Neutro BT
            fillField('info-sil-im-neutro-bt', basicData.sil_neutro_bt, 'kVp'); // Adicionado SIL/IM Neutro BT
            fillField('info-iac-bt', basicData.iac_bt, 'kVp'); // Alterado para kVp conforme solicitado

            // Dados do Terciário
            fillField('info-tensao-terciario', basicData.tensao_terciario, 'kV');
            fillField('info-classe-tensao-terciario', basicData.classe_tensao_terciario, 'kV');
            fillField('info-corrente-nominal-terciario', basicData.corrente_nominal_terciario, 'A');
            fillField('info-nbi-terciario', basicData.nbi_terciario, 'kVp');
            fillField('info-sil-im-terciario', basicData.sil_terciario, 'kVp'); // Adicionado SIL/IM Terciário
            fillField('info-conexao-terciario', basicData.conexao_terciario);
            fillField('info-classe-neutro-terciario', basicData.tensao_bucha_neutro_terciario, 'kVp'); // Adicionado Classe Neutro Terciário
            fillField('info-nbi-neutro-terciario', basicData.nbi_neutro_terciario, 'kVp'); // Adicionado NBI Neutro Terciário
            fillField('info-sil-im-neutro-terciario', basicData.sil_neutro_terciario, 'kVp'); // Adicionado SIL/IM Neutro Terciário
            fillField('info-impedancia-at-terciario', basicData.impedancia_at_terciario, '%');
            fillField('info-impedancia-bt-terciario', basicData.impedancia_bt_terciario, '%');
            fillField('info-iac-terciario', basicData.iac_terciario, 'kVp'); // Alterado para kVp conforme solicitado

            // Tensões de Ensaio
            fillField('info-teste-tensao-aplicada-at', basicData.teste_tensao_aplicada_at, 'kVRms');
            fillField('info-teste-tensao-induzida-at', basicData.teste_tensao_induzida_at, 'kVRms');
            fillField('info-teste-tensao-aplicada-at-tap-maior', basicData.teste_tensao_aplicada_at_tap_maior, 'kVRms');
            fillField('info-teste-tensao-aplicada-at-tap-menor', basicData.teste_tensao_aplicada_at_tap_menor, 'kVRms');
            fillField('info-teste-tensao-induzida-at-tap-maior', basicData.teste_tensao_induzida_at_tap_maior, 'kVRms');
            fillField('info-teste-tensao-induzida-at-tap-menor', basicData.teste_tensao_induzida_at_tap_menor, 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-at', basicData.teste_tensao_aplicada_neutro_at, 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-at', basicData.teste_tensao_induzida_neutro_at, 'kVRms');
            fillField('info-teste-tensao-aplicada-bt', basicData.teste_tensao_aplicada_bt, 'kVRms');
            fillField('info-teste-tensao-induzida-bt', basicData.teste_tensao_induzida_bt, 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-bt', basicData.teste_tensao_aplicada_neutro_bt, 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-bt', basicData.teste_tensao_induzida_neutro_bt, 'kVRms');
            fillField('info-teste-tensao-aplicada-terciario', basicData.teste_tensao_aplicada_terciario, 'kVRms');
            fillField('info-teste-tensao-induzida-terciario', basicData.teste_tensao_induzida_terciario, 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-terciario', basicData.teste_tensao_aplicada_neutro_terciario, 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-terciario', basicData.teste_tensao_induzida_neutro_terciario, 'kVRms');

            // Ensaios e Perdas
            fillField('info-perdas-vazio', basicData.perdas_vazio, 'kW');
            fillField('info-perdas-curto-circuito', basicData.perdas_curto_circuito, 'kW');
            fillField('info-corrente-excitacao', basicData.corrente_excitacao, '%');
            fillField('info-fator-k', basicData.fator_k);
            fillField('info-classe-precisao', basicData.classe_precisao);
            fillField('info-frequencia-ressonancia', basicData.frequencia_ressonancia, 'Hz');

        } else {
            console.log("❌ [FLUXO] TEMPLATE: Nenhum dado do transformador encontrado - exibindo campos vazios");
            console.log("🧹 [FLUXO] TEMPLATE: Limpando template - todos os campos devem mostrar '-'");
            // Limpa os campos se não houver dados (comportamento normal para formulário vazio)

            // Especificações Gerais
            fillField('info-potencia-mva', '', 'MVA');
            fillField('info-frequencia', '', 'Hz');
            fillField('info-tipo-transformador', '');
            fillField('info-grupo-ligacao', '');
            fillField('info-liquido-isolante', '');
            fillField('info-norma-iso', '');
            // Temperaturas e Pesos
            fillField('info-elevacao-oleo-topo', '', '°C');
            fillField('info-elevacao-enrol', '', '°C');
            fillField('info-peso-parte-ativa', '', 'ton');
            fillField('info-peso-tanque', '', 'ton');
            fillField('info-peso-oleo', '', 'ton');
            fillField('info-peso-total', '', 'ton');
            fillField('info-tipo-isolamento', '');

            // Dados da Alta Tensão
            fillField('info-tensao-at', '', 'kV');
            fillField('info-classe-tensao-at', '', 'kV');
            fillField('info-corrente-nominal-at', '', 'A');
            fillField('info-impedancia', '', '%');
            fillField('info-nbi-at', '', 'kVp');
            fillField('info-sil-im-at', '', 'kVp'); // Adicionado SIL/IM AT
            fillField('info-conexao-at', '');
            fillField('info-conexao-neutro-at', ''); // Adicionado Conexao Neutro AT
            fillField('info-tensao-neutro-at', '', 'kVp'); // Adicionado Tensao Neutro AT
            fillField('info-corrente-neutro-at', '', 'A'); // Adicionado Corrente Neutro AT
            fillField('info-nbi-neutro-at', '', 'kVp'); // Adicionado NBI Neutro AT
            fillField('info-sil-im-neutro-at', '', 'kVp'); // Adicionado SIL/IM Neutro AT
            fillField('info-iac-at', '', 'kVp'); // Alterado para kVp conforme solicitado

            // TAPs AT
            fillField('info-tensao-at-tap-maior', '', 'kV');
            fillField('info-tensao-at-tap-menor', '', 'kV');
            fillField('info-corrente-nominal-at-tap-maior', '', 'A');
            fillField('info-corrente-nominal-at-tap-menor', '', 'A');
            fillField('info-impedancia-tap-maior', '', '%');
            fillField('info-impedancia-tap-menor', '', '%');
            fillField('info-classe-tensao-at-tap-maior', '', 'kV');
            fillField('info-classe-tensao-at-tap-menor', '', 'kV');
            fillField('info-conexao-at-tap-maior', '');
            fillField('info-conexao-at-tap-menor', '');
            fillField('info-nbi-at-tap-maior', '', 'kVp');
            fillField('info-nbi-at-tap-menor', '', 'kVp');
            fillField('info-sil-im-at-tap-maior', '', 'kVp');
            fillField('info-sil-im-at-tap-menor', '', 'kVp');
            fillField('info-iac-at-tap-maior', '', 'kVp');
            fillField('info-iac-at-tap-menor', '', 'kVp');
            fillField('info-degrau-comutador', '');
            fillField('info-num-degraus-comutador', '');
            fillField('info-posicao-comutador', '');

            // Dados da Baixa Tensão
            fillField('info-tensao-bt', '', 'kV');
            fillField('info-classe-tensao-bt', '', 'kV');
            fillField('info-corrente-nominal-bt', '', 'A');
            fillField('info-nbi-bt', '', 'kVp');
            fillField('info-sil-im-bt', '', 'kVp'); // Adicionado SIL/IM BT
            fillField('info-conexao-bt', '');
            fillField('info-classe-neutro-bt', '', 'kVp'); // Adicionado Classe Neutro BT
            fillField('info-nbi-neutro-bt', '', 'kVp'); // Adicionado NBI Neutro BT
            fillField('info-sil-im-neutro-bt', '', 'kVp'); // Adicionado SIL/IM Neutro BT
            fillField('info-iac-bt', '', 'kVp'); // Alterado para kVp conforme solicitado

            // Dados do Terciário
            fillField('info-tensao-terciario', '', 'kV');
            fillField('info-classe-tensao-terciario', '', 'kV');
            fillField('info-corrente-nominal-terciario', '', 'A');
            fillField('info-nbi-terciario', '', 'kVp');
            fillField('info-sil-im-terciario', '', 'kVp'); // Adicionado SIL/IM Terciário
            fillField('info-conexao-terciario', '');
            fillField('info-classe-neutro-terciario', '', 'kVp'); // Adicionado Classe Neutro Terciário
            fillField('info-nbi-neutro-terciario', '', 'kVp'); // Adicionado NBI Neutro Terciário
            fillField('info-sil-im-neutro-terciario', '', 'kVp'); // Adicionado SIL/IM Neutro Terciário
            fillField('info-impedancia-at-terciario', '', '%');
            fillField('info-impedancia-bt-terciario', '', '%');
            fillField('info-iac-terciario', '', 'kVp'); // Alterado para kVp conforme solicitado

            // Tensões de Ensaio
            fillField('info-teste-tensao-aplicada-at', '', 'kVRms');
            fillField('info-teste-tensao-induzida-at', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-at-tap-maior', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-at-tap-menor', '', 'kVRms');
            fillField('info-teste-tensao-induzida-at-tap-maior', '', 'kVRms');
            fillField('info-teste-tensao-induzida-at-tap-menor', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-at', '', 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-at', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-bt', '', 'kVRms');
            fillField('info-teste-tensao-induzida-bt', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-bt', '', 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-bt', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-terciario', '', 'kVRms');
            fillField('info-teste-tensao-induzida-terciario', '', 'kVRms');
            fillField('info-teste-tensao-aplicada-neutro-terciario', '', 'kVRms');
            fillField('info-teste-tensao-induzida-neutro-terciario', '', 'kVRms');

            // Ensaios e Perdas
            fillField('info-perdas-vazio', '', 'kW');
            fillField('info-perdas-curto-circuito', '', 'kW');
            fillField('info-corrente-excitacao', '', '%');
            fillField('info-fator-k', '');
            fillField('info-classe-precisao', '');
            fillField('info-frequencia-ressonancia', '', 'Hz');
        }
    } catch (error) {
        console.error('[common_module] loadAndPopulateTransformerInfo: Erro ao carregar ou preencher o painel de informações do transformador:', error);
        targetElement.innerHTML = `
            <div class="alert alert-danger m-0 p-2" style="font-size: 0.75rem;">
                Erro ao carregar informações do transformador.
            </div>
        `;
    } finally {
        console.log(`[common_module] loadAndPopulateTransformerInfo: Concluído para ${targetElementId}`);
    }
}

// Função para verificar se todos os dados necessários para perdas estão completos
function checkRequiredDataForLosses(basicData) {
    console.log('[common_module] checkRequiredDataForLosses: Verificando dados necessários para perdas');

    // Campos obrigatórios para cálculos de perdas (mínimos essenciais)
    const requiredFields = [
        { field: 'potencia_mva', name: 'Potência (MVA)' },
        { field: 'frequencia', name: 'Frequência' },
        { field: 'tipo_transformador', name: 'Tipo do Transformador' },
        { field: 'tensao_bt', name: 'Tensão BT' },
        { field: 'corrente_nominal_bt', name: 'Corrente Nominal BT' }
    ];

    // Campos importantes para perdas em carga (verificação adicional)
    const loadLossFields = [
        { field: 'impedancia', name: 'Impedância' },
        { field: 'tensao_at', name: 'Tensão AT' },
        { field: 'corrente_nominal_at', name: 'Corrente Nominal AT' }
    ];

    const missingFields = [];
    const validFields = [];
    const missingLoadFields = [];

    // Verifica campos obrigatórios básicos
    requiredFields.forEach(({ field, name }) => {
        const value = basicData[field];
        if (value === undefined || value === null || value === '' ||
            (typeof value === 'number' && (isNaN(value) || value <= 0))) {
            missingFields.push(name);
        } else {
            validFields.push({ field, name });
        }
    });

    // Verifica campos para perdas em carga
    loadLossFields.forEach(({ field, name }) => {
        const value = basicData[field];
        if (value === undefined || value === null || value === '' ||
            (typeof value === 'number' && (isNaN(value) || value <= 0))) {
            missingLoadFields.push(name);
        } else {
            validFields.push({ field, name });
        }
    });

    return {
        isComplete: missingFields.length === 0, // Só considera completo se campos básicos estão OK
        missingFields,
        missingLoadFields,
        validFields,
        canCalculateNoLoad: missingFields.length === 0, // Pode calcular perdas em vazio
        canCalculateLoad: missingFields.length === 0 && missingLoadFields.length === 0 // Pode calcular perdas em carga
    };
}

// Função para destacar dados herdados importantes para cálculos de perdas
function highlightInheritedDataForLosses(basicData) {
    console.log('[common_module] highlightInheritedDataForLosses: Verificando e destacando dados para perdas');

    const dataCheck = checkRequiredDataForLosses(basicData);

    // Remove indicadores anteriores
    const existingIndicator = document.querySelector('.info-card-header .inheritance-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Remove destaques anteriores
    document.querySelectorAll('.inherited-data-highlight').forEach(el => {
        el.classList.remove('inherited-data-highlight');
    });
    document.querySelectorAll('.inherited-field-container').forEach(el => {
        el.classList.remove('inherited-field-container');
        el.removeAttribute('title');
    });

    const cardHeader = document.querySelector('.info-card-header span');
    if (!cardHeader) return;

    if (dataCheck.isComplete) {
        // Todos os dados necessários estão completos - mostrar herança
        console.log('[common_module] Todos os dados necessários para perdas estão completos');

        // Lista expandida de campos importantes para perdas com suas descrições
        const lossesImportantFields = [
            // Dados Gerais - essenciais para perdas
            { id: 'info-potencia-mva', field: 'potencia_mva', description: 'Usado em perdas em carga (perdas por unidade)' },
            { id: 'info-frequencia', field: 'frequencia', description: 'Usado em perdas em vazio e carga' },
            { id: 'info-tipo-transformador', field: 'tipo_transformador', description: 'Usado para fatores de correção (√3)' },

            // Dados AT - essenciais para perdas em carga
            { id: 'info-tensao-at', field: 'tensao_at', description: 'Tensão nominal AT para perdas em carga' },
            { id: 'info-corrente-nominal-at', field: 'corrente_nominal_at', description: 'Corrente nominal AT para perdas em carga' },
            { id: 'info-impedancia', field: 'impedancia', description: 'Impedância nominal para perdas em carga' },

            // TAPs AT - para perdas em carga nos diferentes taps
            { id: 'info-tensao-at-tap-maior', field: 'tensao_at_tap_maior', description: 'Tensão TAP+ para perdas em carga' },
            { id: 'info-tensao-at-tap-menor', field: 'tensao_at_tap_menor', description: 'Tensão TAP- para perdas em carga' },
            { id: 'info-corrente-nominal-at-tap-maior', field: 'corrente_nominal_at_tap_maior', description: 'Corrente TAP+ para perdas em carga' },
            { id: 'info-corrente-nominal-at-tap-menor', field: 'corrente_nominal_at_tap_menor', description: 'Corrente TAP- para perdas em carga' },
            { id: 'info-impedancia-tap-maior', field: 'impedancia_tap_maior', description: 'Impedância TAP+ para perdas em carga' },
            { id: 'info-impedancia-tap-menor', field: 'impedancia_tap_menor', description: 'Impedância TAP- para perdas em carga' },

            // Dados BT - essenciais para perdas em vazio
            { id: 'info-tensao-bt', field: 'tensao_bt', description: 'Tensão BT para perdas em vazio' },
            { id: 'info-corrente-nominal-bt', field: 'corrente_nominal_bt', description: 'Corrente BT para perdas em vazio' },

            // Dados Terciário - essenciais para perdas em vazio quando BT excede limites SUT
            { id: 'info-tensao-terciario', field: 'tensao_terciario', description: 'Tensão Terciário para perdas em vazio (quando BT excede limites SUT)' },
            { id: 'info-corrente-nominal-terciario', field: 'corrente_nominal_terciario', description: 'Corrente Terciário para perdas em vazio (quando BT excede limites SUT)' }
        ];

        // Adiciona destaque visual e tooltip para campos importantes
        lossesImportantFields.forEach(({ id, field, description }) => {
            const element = document.getElementById(id);
            if (element && basicData[field] !== undefined && basicData[field] !== null && basicData[field] !== '') {
                // Adiciona classe de destaque
                element.classList.add('inherited-data-highlight');

                // Adiciona tooltip explicativo
                const parentElement = element.parentElement;
                if (parentElement) {
                    parentElement.setAttribute('title', `Herdado: ${description}`);
                    parentElement.classList.add('inherited-field-container');
                }

                console.log(`[common_module] Campo ${field} destacado como herdado para perdas:`, basicData[field]);
            }
        });

        // Adiciona indicador visual de herança no cabeçalho do card
        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-success ms-2';
        indicator.innerHTML = '<i class="fas fa-link me-1"></i>Dados herdados para perdas (vazio + carga)';
        indicator.title = 'Campos destacados são herdados automaticamente do Transformer Inputs para cálculos de perdas';
        cardHeader.appendChild(indicator);

    } else {
        // Dados incompletos - mostrar o que falta e limpar resultados
        console.log('[common_module] Dados incompletos para perdas. Faltam:', dataCheck.missingFields);

        // Limpar resultados de cálculos quando dados estão incompletos
        clearModuleResults('losses');

        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-warning ms-2';
        indicator.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Faltam dados: ${dataCheck.missingFields.join(', ')}`;
        indicator.title = 'Complete os dados básicos do transformador para habilitar os cálculos de perdas';
        cardHeader.appendChild(indicator);
    }
}

// ===== FUNÇÕES DE VALIDAÇÃO PARA CADA MÓDULO =====

// Função para verificar dados necessários para curto-circuito
function checkRequiredDataForShortCircuit(basicData) {
    console.log('[common_module] checkRequiredDataForShortCircuit: Verificando dados necessários para curto-circuito');

    const requiredFields = [
        { field: 'potencia_mva', name: 'Potência (MVA)' },
        { field: 'frequencia', name: 'Frequência' },
        { field: 'tensao_at', name: 'Tensão AT' },
        { field: 'tensao_bt', name: 'Tensão BT' },
        { field: 'impedancia', name: 'Impedância' }
    ];

    const missingFields = [];
    const validFields = [];

    requiredFields.forEach(({ field, name }) => {
        const value = basicData[field];
        if (value === undefined || value === null || value === '' ||
            (typeof value === 'number' && (isNaN(value) || value <= 0))) {
            missingFields.push(name);
        } else {
            validFields.push({ field, name });
        }
    });

    return {
        isComplete: missingFields.length === 0,
        missingFields,
        validFields
    };
}

// Função para destacar dados herdados para curto-circuito
function highlightInheritedDataForShortCircuit(basicData) {
    console.log('[common_module] highlightInheritedDataForShortCircuit: Verificando e destacando dados para curto-circuito');

    const dataCheck = checkRequiredDataForShortCircuit(basicData);

    // Remove indicadores anteriores
    const existingIndicator = document.querySelector('.info-card-header .inheritance-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Remove destaques anteriores
    document.querySelectorAll('.inherited-data-highlight').forEach(el => {
        el.classList.remove('inherited-data-highlight');
    });
    document.querySelectorAll('.inherited-field-container').forEach(el => {
        el.classList.remove('inherited-field-container');
        el.removeAttribute('title');
    });

    const cardHeader = document.querySelector('.info-card-header span');
    if (!cardHeader) return;

    if (dataCheck.isComplete) {
        // Todos os dados necessários estão completos - mostrar herança
        console.log('[common_module] Todos os dados necessários para curto-circuito estão completos');

        const shortCircuitImportantFields = [
            { id: 'info-potencia-mva', field: 'potencia_mva', description: 'Usado para cálculo de corrente de curto-circuito' },
            { id: 'info-frequencia', field: 'frequencia', description: 'Usado para cálculo de reatância' },
            { id: 'info-tensao-at', field: 'tensao_at', description: 'Usado para cálculo de corrente de curto-circuito' },
            { id: 'info-tensao-bt', field: 'tensao_bt', description: 'Usado para cálculo de corrente de curto-circuito' },
            { id: 'info-impedancia', field: 'impedancia', description: 'Usado para cálculo de impedância de curto-circuito' }
        ];

        // Adiciona destaque visual e tooltip para campos importantes
        shortCircuitImportantFields.forEach(({ id, field, description }) => {
            const element = document.getElementById(id);
            if (element && basicData[field] !== undefined && basicData[field] !== null && basicData[field] !== '') {
                element.classList.add('inherited-data-highlight');
                const parentElement = element.parentElement;
                if (parentElement) {
                    parentElement.setAttribute('title', `Herdado: ${description}`);
                    parentElement.classList.add('inherited-field-container');
                }
                console.log(`[common_module] Campo ${field} destacado como herdado para curto-circuito:`, basicData[field]);
            }
        });

        // Adiciona indicador visual de herança no cabeçalho do card
        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-success ms-2';
        indicator.innerHTML = '<i class="fas fa-link me-1"></i>Dados herdados para curto-circuito';
        indicator.title = 'Campos destacados são herdados automaticamente do Transformer Inputs';
        cardHeader.appendChild(indicator);

    } else {
        // Dados incompletos - mostrar o que falta e limpar resultados
        console.log('[common_module] Dados incompletos para curto-circuito. Faltam:', dataCheck.missingFields);

        // Limpar resultados de cálculos quando dados estão incompletos
        clearModuleResults('short_circuit');

        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-warning ms-2';
        indicator.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Faltam dados: ${dataCheck.missingFields.join(', ')}`;
        indicator.title = 'Complete os dados básicos do transformador para habilitar os cálculos de curto-circuito';
        cardHeader.appendChild(indicator);
    }
}

// Função para verificar dados necessários para elevação de temperatura
function checkRequiredDataForTemperatureRise(basicData) {
    console.log('[common_module] checkRequiredDataForTemperatureRise: Verificando dados necessários para elevação de temperatura');

    const requiredFields = [
        { field: 'peso_total', name: 'Peso Total' },
        { field: 'peso_oleo', name: 'Peso do Óleo' },
        { field: 'potencia_mva', name: 'Potência (MVA)' }
    ];

    const missingFields = [];
    const validFields = [];

    requiredFields.forEach(({ field, name }) => {
        const value = basicData[field];
        if (value === undefined || value === null || value === '' ||
            (typeof value === 'number' && (isNaN(value) || value <= 0))) {
            missingFields.push(name);
        } else {
            validFields.push({ field, name });
        }
    });

    return {
        isComplete: missingFields.length === 0,
        missingFields,
        validFields
    };
}

// Função para destacar dados herdados para elevação de temperatura
function highlightInheritedDataForTemperatureRise(basicData) {
    console.log('[common_module] highlightInheritedDataForTemperatureRise: Verificando e destacando dados para elevação de temperatura');

    const dataCheck = checkRequiredDataForTemperatureRise(basicData);

    // Remove indicadores anteriores
    const existingIndicator = document.querySelector('.info-card-header .inheritance-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Remove destaques anteriores
    document.querySelectorAll('.inherited-data-highlight').forEach(el => {
        el.classList.remove('inherited-data-highlight');
    });
    document.querySelectorAll('.inherited-field-container').forEach(el => {
        el.classList.remove('inherited-field-container');
        el.removeAttribute('title');
    });

    const cardHeader = document.querySelector('.info-card-header span');
    if (!cardHeader) return;

    if (dataCheck.isComplete) {
        // Todos os dados necessários estão completos - mostrar herança
        console.log('[common_module] Todos os dados necessários para elevação de temperatura estão completos');

        const temperatureRiseImportantFields = [
            { id: 'info-peso-total', field: 'peso_total', description: 'Usado para cálculo de constante de tempo térmica' },
            { id: 'info-peso-oleo', field: 'peso_oleo', description: 'Usado para cálculo de constante de tempo térmica' },
            { id: 'info-potencia-mva', field: 'potencia_mva', description: 'Usado para cálculo de perdas totais' }
        ];

        // Adiciona destaque visual e tooltip para campos importantes
        temperatureRiseImportantFields.forEach(({ id, field, description }) => {
            const element = document.getElementById(id);
            if (element && basicData[field] !== undefined && basicData[field] !== null && basicData[field] !== '') {
                element.classList.add('inherited-data-highlight');
                const parentElement = element.parentElement;
                if (parentElement) {
                    parentElement.setAttribute('title', `Herdado: ${description}`);
                    parentElement.classList.add('inherited-field-container');
                }
                console.log(`[common_module] Campo ${field} destacado como herdado para elevação de temperatura:`, basicData[field]);
            }
        });

        // Adiciona indicador visual de herança no cabeçalho do card
        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-success ms-2';
        indicator.innerHTML = '<i class="fas fa-link me-1"></i>Dados herdados para elevação de temperatura';
        indicator.title = 'Campos destacados são herdados automaticamente do Transformer Inputs';
        cardHeader.appendChild(indicator);

    } else {
        // Dados incompletos - mostrar o que falta e limpar resultados
        console.log('[common_module] Dados incompletos para elevação de temperatura. Faltam:', dataCheck.missingFields);

        // Limpar resultados de cálculos quando dados estão incompletos
        clearModuleResults('temperature_rise');

        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-warning ms-2';
        indicator.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Faltam dados: ${dataCheck.missingFields.join(', ')}`;
        indicator.title = 'Complete os dados básicos do transformador para habilitar os cálculos de elevação de temperatura';
        cardHeader.appendChild(indicator);
    }
}

// Função para verificar dados necessários para tensão induzida
function checkRequiredDataForInducedVoltage(basicData) {
    console.log('[common_module] checkRequiredDataForInducedVoltage: Verificando dados necessários para tensão induzida');

    const requiredFields = [
        { field: 'tensao_at', name: 'Tensão AT' },
        { field: 'tensao_bt', name: 'Tensão BT' },
        { field: 'frequencia', name: 'Frequência' },
        { field: 'teste_tensao_induzida_at', name: 'Tensão de Ensaio Induzida AT' }
    ];

    const missingFields = [];
    const validFields = [];

    requiredFields.forEach(({ field, name }) => {
        const value = basicData[field];
        if (value === undefined || value === null || value === '' ||
            (typeof value === 'number' && (isNaN(value) || value <= 0))) {
            missingFields.push(name);
        } else {
            validFields.push({ field, name });
        }
    });

    return {
        isComplete: missingFields.length === 0,
        missingFields,
        validFields
    };
}

// Função para destacar dados herdados para tensão induzida
function highlightInheritedDataForInducedVoltage(basicData) {
    console.log('[common_module] highlightInheritedDataForInducedVoltage: Verificando e destacando dados para tensão induzida');

    const dataCheck = checkRequiredDataForInducedVoltage(basicData);

    // Remove indicadores anteriores
    const existingIndicator = document.querySelector('.info-card-header .inheritance-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Remove destaques anteriores
    document.querySelectorAll('.inherited-data-highlight').forEach(el => {
        el.classList.remove('inherited-data-highlight');
    });
    document.querySelectorAll('.inherited-field-container').forEach(el => {
        el.classList.remove('inherited-field-container');
        el.removeAttribute('title');
    });

    const cardHeader = document.querySelector('.info-card-header span');
    if (!cardHeader) return;

    if (dataCheck.isComplete) {
        // Todos os dados necessários estão completos - mostrar herança
        console.log('[common_module] Todos os dados necessários para tensão induzida estão completos');

        const inducedVoltageImportantFields = [
            { id: 'info-tensao-at', field: 'tensao_at', description: 'Usado para cálculo de tensão de prova' },
            { id: 'info-tensao-bt', field: 'tensao_bt', description: 'Usado para cálculo de tensão de prova' },
            { id: 'info-frequencia', field: 'frequencia', description: 'Usado para cálculo de indução de teste' },
            { id: 'info-teste-tensao-induzida-at', field: 'teste_tensao_induzida_at', description: 'Tensão de ensaio induzida para AT' }
        ];

        // Adiciona destaque visual e tooltip para campos importantes
        inducedVoltageImportantFields.forEach(({ id, field, description }) => {
            const element = document.getElementById(id);
            if (element && basicData[field] !== undefined && basicData[field] !== null && basicData[field] !== '') {
                element.classList.add('inherited-data-highlight');
                const parentElement = element.parentElement;
                if (parentElement) {
                    parentElement.setAttribute('title', `Herdado: ${description}`);
                    parentElement.classList.add('inherited-field-container');
                }
                console.log(`[common_module] Campo ${field} destacado como herdado para tensão induzida:`, basicData[field]);
            }
        });

        // Adiciona indicador visual de herança no cabeçalho do card
        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-success ms-2';
        indicator.innerHTML = '<i class="fas fa-link me-1"></i>Dados herdados para tensão induzida';
        indicator.title = 'Campos destacados são herdados automaticamente do Transformer Inputs';
        cardHeader.appendChild(indicator);

    } else {
        // Dados incompletos - mostrar o que falta e limpar resultados
        console.log('[common_module] Dados incompletos para tensão induzida. Faltam:', dataCheck.missingFields);

        // Limpar resultados de cálculos quando dados estão incompletos
        clearModuleResults('induced_voltage');

        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-warning ms-2';
        indicator.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Faltam dados: ${dataCheck.missingFields.join(', ')}`;
        indicator.title = 'Complete os dados básicos do transformador para habilitar os cálculos de tensão induzida';
        cardHeader.appendChild(indicator);
    }
}

// Função para verificar dados necessários para tensão aplicada
function checkRequiredDataForAppliedVoltage(basicData) {
    console.log('[common_module] checkRequiredDataForAppliedVoltage: Verificando dados necessários para tensão aplicada');

    const requiredFields = [
        { field: 'frequencia', name: 'Frequência' }
    ];

    // Verifica se pelo menos uma tensão de ensaio aplicada está preenchida
    const hasTestVoltage = (
        (basicData.teste_tensao_aplicada_at && basicData.teste_tensao_aplicada_at !== '') ||
        (basicData.teste_tensao_aplicada_bt && basicData.teste_tensao_aplicada_bt !== '') ||
        (basicData.teste_tensao_aplicada_terciario && basicData.teste_tensao_aplicada_terciario !== '')
    );

    if (!hasTestVoltage) {
        requiredFields.push({ field: 'teste_tensao_aplicada', name: 'Pelo menos uma Tensão de Ensaio Aplicada (AT/BT/Terciário)' });
    }

    const missingFields = [];
    const validFields = [];

    requiredFields.forEach(({ field, name }) => {
        if (field === 'teste_tensao_aplicada') {
            // Campo especial para verificar tensões de ensaio
            if (!hasTestVoltage) {
                missingFields.push(name);
            } else {
                validFields.push({ field, name });
            }
        } else {
            const value = basicData[field];
            if (value === undefined || value === null || value === '' ||
                (typeof value === 'number' && (isNaN(value) || value <= 0))) {
                missingFields.push(name);
            } else {
                validFields.push({ field, name });
            }
        }
    });

    return {
        isComplete: missingFields.length === 0,
        missingFields,
        validFields
    };
}

// Função para destacar dados herdados para tensão aplicada
function highlightInheritedDataForAppliedVoltage(basicData) {
    console.log('[common_module] highlightInheritedDataForAppliedVoltage: Verificando e destacando dados para tensão aplicada');

    const dataCheck = checkRequiredDataForAppliedVoltage(basicData);

    // Remove indicadores anteriores
    const existingIndicator = document.querySelector('.info-card-header .inheritance-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Remove destaques anteriores
    document.querySelectorAll('.inherited-data-highlight').forEach(el => {
        el.classList.remove('inherited-data-highlight');
    });
    document.querySelectorAll('.inherited-field-container').forEach(el => {
        el.classList.remove('inherited-field-container');
        el.removeAttribute('title');
    });

    const cardHeader = document.querySelector('.info-card-header span');
    if (!cardHeader) return;

    if (dataCheck.isComplete) {
        // Todos os dados necessários estão completos - mostrar herança
        console.log('[common_module] Todos os dados necessários para tensão aplicada estão completos');

        const appliedVoltageImportantFields = [
            { id: 'info-frequencia', field: 'frequencia', description: 'Usado para cálculo de frequência de ressonância' },
            { id: 'info-teste-tensao-aplicada-at', field: 'teste_tensao_aplicada_at', description: 'Tensão de ensaio aplicada para AT' },
            { id: 'info-teste-tensao-aplicada-bt', field: 'teste_tensao_aplicada_bt', description: 'Tensão de ensaio aplicada para BT' },
            { id: 'info-teste-tensao-aplicada-terciario', field: 'teste_tensao_aplicada_terciario', description: 'Tensão de ensaio aplicada para Terciário' }
        ];

        // Adiciona destaque visual e tooltip para campos importantes
        appliedVoltageImportantFields.forEach(({ id, field, description }) => {
            const element = document.getElementById(id);
            if (element && basicData[field] !== undefined && basicData[field] !== null && basicData[field] !== '') {
                element.classList.add('inherited-data-highlight');
                const parentElement = element.parentElement;
                if (parentElement) {
                    parentElement.setAttribute('title', `Herdado: ${description}`);
                    parentElement.classList.add('inherited-field-container');
                }
                console.log(`[common_module] Campo ${field} destacado como herdado para tensão aplicada:`, basicData[field]);
            }
        });

        // Adiciona indicador visual de herança no cabeçalho do card
        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-success ms-2';
        indicator.innerHTML = '<i class="fas fa-link me-1"></i>Dados herdados para tensão aplicada';
        indicator.title = 'Campos destacados são herdados automaticamente do Transformer Inputs';
        cardHeader.appendChild(indicator);

    } else {
        // Dados incompletos - mostrar o que falta e limpar resultados
        console.log('[common_module] Dados incompletos para tensão aplicada. Faltam:', dataCheck.missingFields);

        // Limpar resultados de cálculos quando dados estão incompletos
        clearModuleResults('applied_voltage');

        const indicator = document.createElement('small');
        indicator.className = 'inheritance-indicator text-warning ms-2';
        indicator.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Faltam dados: ${dataCheck.missingFields.join(', ')}`;
        indicator.title = 'Complete os dados básicos do transformador para habilitar os cálculos de tensão aplicada';
        cardHeader.appendChild(indicator);
    }
}

// Função para limpar resultados de cálculos quando dados estão incompletos
function clearModuleResults(moduleName) {
    console.log(`[common_module] clearModuleResults: Limpando resultados para módulo ${moduleName}`);

    try {
        switch (moduleName) {
            case 'losses':
                // Limpar resultados de perdas
                const perdasVazioContainer = document.getElementById('resultados-perdas-vazio');
                if (perdasVazioContainer) {
                    perdasVazioContainer.innerHTML = '<div class="text-muted text-center p-3">Complete os dados básicos para habilitar os cálculos.</div>';
                }

                const perdasCargaContainer = document.getElementById('resultados-perdas-carga');
                if (perdasCargaContainer) {
                    perdasCargaContainer.innerHTML = '<div class="text-muted text-center p-3">Complete os dados básicos para habilitar os cálculos.</div>';
                }
                break;

            case 'short_circuit':
                // Limpar resultados de curto-circuito
                const resultFields = ['isc-sym-result', 'isc-peak-result', 'delta-impedance-result'];
                resultFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) field.value = '-';
                });

                const statusElement = document.getElementById('impedance-check-status');
                if (statusElement) statusElement.innerHTML = '-';

                const graphDiv = document.getElementById('impedance-variation-graph');
                if (graphDiv) {
                    graphDiv.innerHTML = '<div class="text-muted text-center p-3">Complete os dados básicos para habilitar os cálculos.</div>';
                    graphDiv.classList.add('plotly-graph-placeholder');
                }
                break;

            case 'temperature_rise':
                // Limpar resultados de elevação de temperatura
                const tempFields = ['avg-winding-temp', 'avg-winding-rise', 'top-oil-rise', 'ptot-used', 'tau0-result'];
                tempFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) field.value = '';
                });
                break;

            case 'induced_voltage':
                // Limpar resultados de tensão induzida
                const inducedContainer = document.getElementById('resultado-tensao-induzida');
                if (inducedContainer) {
                    inducedContainer.innerHTML = '<div class="text-muted text-center py-3">Complete os dados básicos para habilitar os cálculos.</div>';
                }

                const freqTableContainer = document.getElementById('frequency-table-container');
                if (freqTableContainer) {
                    freqTableContainer.innerHTML = '';
                }
                break;

            case 'applied_voltage':
                // Limpar resultados de tensão aplicada
                const appliedContainer = document.getElementById('applied-voltage-results');
                if (appliedContainer) {
                    appliedContainer.innerHTML = '<div class="text-center text-muted py-3"><i class="fas fa-info-circle me-2"></i>Complete os dados básicos para habilitar os cálculos.</div>';
                }

                const recommendationContainer = document.getElementById('resonant-system-recommendation');
                if (recommendationContainer) {
                    recommendationContainer.innerHTML = 'Complete os dados básicos para habilitar os cálculos.';
                }
                break;



            default:
                console.warn(`[common_module] clearModuleResults: Módulo ${moduleName} não reconhecido`);
        }

        console.log(`[common_module] clearModuleResults: Resultados limpos para ${moduleName}`);

    } catch (error) {
        console.error(`[common_module] clearModuleResults: Erro ao limpar resultados para ${moduleName}:`, error);
    }
}

// Função para atualizar o painel de informações globalmente
async function updateGlobalInfoPanel() {
    console.log('[common_module] updateGlobalInfoPanel: Iniciando');
    // Esta função pode ser chamada quando dados do transformador são atualizados
    // Procura por painéis de informação em todas as páginas e os atualiza
    const infoPanels = document.querySelectorAll('.transformer-info-panel');
    if (infoPanels.length > 0) {
        console.log(`[common_module] updateGlobalInfoPanel: Encontrados ${infoPanels.length} painéis. Atualizando o primeiro.`);
        // Se encontrar painéis, recarrega as informações
        const targetElementId = infoPanels[0].closest('[id]')?.id;
        if (targetElementId) {
            await loadAndPopulateTransformerInfo(targetElementId);
        } else {
             console.warn('[common_module] updateGlobalInfoPanel: Não foi possível encontrar o ID do elemento pai do painel.');
        }
    } else {
        console.log('[common_module] updateGlobalInfoPanel: Nenhum painel de informações encontrado para atualizar.');
    }
    console.log('[common_module] updateGlobalInfoPanel: Concluído');
}

// Configura listener para atualização automática quando dados do transformador mudam
function setupAutoUpdateListener() {
    console.log('[common_module] setupAutoUpdateListener: Configurando listener para transformerDataUpdated');

    // Listener principal para transformerDataUpdated
    document.addEventListener('transformerDataUpdated', async (event) => {
        console.log('[common_module] Evento transformerDataUpdated recebido, atualizando painéis:', event.detail);

        // Reabilitado - problema era a limpeza forçada de cache na função loadAndPopulateTransformerInfo
        await updateGlobalInfoPanel();

        // Ocultar resultados de todos os módulos dependentes quando dados básicos mudam
        hideResultsOnInheritedDataChange();
    });

    // Listener adicional para mudanças diretas nos dados básicos
    document.addEventListener('basicDataChanged', async (event) => {
        console.log('[common_module] Evento basicDataChanged recebido:', event.detail);
        hideResultsOnInheritedDataChange();
    });

    console.log('[common_module] setupAutoUpdateListener: Listeners de atualização automática configurados');
}

// Função para ocultar resultados quando dados herdados são modificados
function hideResultsOnInheritedDataChange() {
    console.log('🔄 [common_module] INICIANDO ocultação de resultados devido a mudanças nos dados herdados');

    // Lista de módulos que dependem de dados herdados
    const dependentModules = ['losses', 'applied_voltage', 'induced_voltage', 'temperature_rise', 'short_circuit'];

    console.log('📋 [common_module] Módulos que serão afetados:', dependentModules);

    dependentModules.forEach(module => {
        console.log(`🧹 [common_module] Limpando resultados do módulo: ${module}`);
        clearModuleResults(module);
    });

    // Mostrar mensagem indicando necessidade de recálculo
    showRecalculationMessage();

    console.log('✅ [common_module] Ocultação de resultados CONCLUÍDA');
}

// Função para mostrar mensagem de necessidade de recálculo
function showRecalculationMessage() {
    // Criar ou atualizar mensagem de recálculo
    let messageElement = document.getElementById('recalculation-message');
    if (!messageElement) {
        messageElement = document.createElement('div');
        messageElement.id = 'recalculation-message';
        messageElement.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        messageElement.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';

        messageElement.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Dados alterados!</strong> Os resultados foram ocultados.
            Recalcule nos módulos afetados.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(messageElement);

        // Auto-remover após 5 segundos
        setTimeout(() => {
            if (messageElement && messageElement.parentNode) {
                messageElement.remove();
            }
        }, 5000);
    }
}

// Limpeza automática de cache para módulos específicos (DESABILITADA)
function autoCleanModuleCache(moduleName) {
    console.log(`[common_module] autoCleanModuleCache: DESABILITADA para evitar limpeza indevida de dados básicos`);
    // Função desabilitada temporariamente para evitar limpeza dos dados básicos
    return;
}

// Inicializa o listener quando o DOM estiver pronto
if (document.readyState === 'loading') {
    console.log('[common_module] DOMContentLoaded: Configurando listener para setupAutoUpdateListener');
    document.addEventListener('DOMContentLoaded', setupAutoUpdateListener);
} else {
    console.log('[common_module] DOM já pronto: Executando setupAutoUpdateListener diretamente');
    setupAutoUpdateListener();
}

// Limpa cache automaticamente para módulos problemáticos (DESABILITADO temporariamente)
// autoCleanModuleCache('history');
// autoCleanModuleCache('standards');

// Inicialização automática quando o DOM estiver pronto (REMOVIDA - já feita acima)
// document.addEventListener('DOMContentLoaded', () => {
//     console.log('[common_module] DOM carregado, configurando listeners automáticos');
//     setupAutoUpdateListener();
// });

// Exportar funções para uso em outros módulos
window.hideResultsOnInheritedDataChange = hideResultsOnInheritedDataChange;
window.showRecalculationMessage = showRecalculationMessage;

// Exporta as funções para serem usadas pelos scripts de módulo
export { loadAndPopulateTransformerInfo, transformerDataStore, updateGlobalInfoPanel };