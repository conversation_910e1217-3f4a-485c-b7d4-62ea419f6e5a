"""
Test data structures and serialization models for all test modules.
Ensures data integrity across database storage, history records, and PDF/Excel report generation.
"""

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
import json


# --- Base Serialization Models ---

class BaseTestResult(BaseModel):
    """Base class for all test result serialization."""
    module_name: str
    calculation_timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0"
    status: str = "completed"  # completed, error, partial
    alerts: List[str] = Field(default_factory=list)
    critical_issues: List[str] = Field(default_factory=list)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BankConfiguration(BaseModel):
    """Standardized bank configuration structure."""
    tensao_disp_kv: Optional[float] = None
    group_info: Optional[str] = None
    cs_config: Optional[str] = None
    q_config: Optional[str] = None
    q_provided_mvar: Optional[float] = None
    q_efetiva_banco_mvar: Optional[float] = None
    eps_within_limits: bool = True
    power_ideal_met: bool = True
    available_configurations: List[Dict[str, Any]] = Field(default_factory=list)
    is_default: bool = False
    is_initial_display_default: bool = False


class StatusValidation(BaseModel):
    """Standardized status validation structure."""
    status_global: str = "OK"
    status_v_menor: str = "OK"
    status_v_maior: str = "OK"
    critical_alerts: List[str] = Field(default_factory=list)
    warning_alerts: List[str] = Field(default_factory=list)
    info_alerts: List[str] = Field(default_factory=list)


class LimitsInfo(BaseModel):
    """Standardized limits information structure."""
    eps_current_limit_positive_a: float
    eps_current_limit_negative_a: float
    dut_power_limit_kw: float
    sut_at_max_voltage_kv: float = 140.0
    ct_current_limit_a: float = 2000.0
    sut_eps_type: str = "Trifásico"  # Tipo SUT/EPS determinado pelo backend
    capacitor_power_limits_by_voltage: Dict[str, Dict[str, Dict[str, float]]] = Field(default_factory=dict)


# --- Losses Module Serialization ---

class LossesTestParams(BaseModel):
    """Test parameters for a specific scenario."""
    tensao_kv: Optional[float] = None
    corrente_a: Optional[float] = None
    pativa_kw: Optional[float] = None
    pteste_mva: Optional[float] = None
    q_teste_mvar: Optional[float] = None
    corrente_eps_sf_a: Optional[float] = None
    corrente_eps_cf_a: Optional[float] = None


class LossesScenarioResult(BaseModel):
    """Results for a specific losses test scenario."""
    nome_cenario_teste: str
    test_params_cenario: LossesTestParams
    status_global: str = "OK"
    status_v_menor: str = "OK"
    status_v_maior: str = "OK"
    banco_sf_data: BankConfiguration
    banco_cf_data: BankConfiguration
    sut_eps_analysis: Dict[str, Any] = Field(default_factory=dict)


class LossesTapResult(BaseModel):
    """Results for a specific tap in losses testing."""
    nome_tap: str
    cenarios_do_tap: List[LossesScenarioResult]


class LossesResult(BaseTestResult):
    """Complete losses module serialization."""
    module_name: str = "losses"

    # Form data
    form_data: Dict[str, Any] = Field(default_factory=dict)

    # Results structure
    condicoes_nominais: Dict[str, Any] = Field(default_factory=dict)
    cenarios_detalhados_por_tap: List[LossesTapResult] = Field(default_factory=list)
    limites_info: LimitsInfo

    # Bank selections (user choices)
    bank_selections: Dict[str, Any] = Field(default_factory=dict)

    # Summary information
    info_geral_banco_capacitores: str = ""
    alertas_gerais_status: List[str] = Field(default_factory=list)


# --- Induced Voltage Module Serialization ---

class InducedVoltageTestParams(BaseModel):
    """Test parameters for induced voltage testing."""
    freq_teste: float
    capacitancia: float
    tipo_transformador: str
    tipo_aco: str = "H110-27"
    tensao_prova: Optional[float] = None
    tensao_at: Optional[float] = None
    tensao_bt: Optional[float] = None
    tensao_terciario: Optional[float] = None


class InducedVoltageSutAnalysis(BaseModel):
    """SUT analysis for induced voltage testing."""
    sut_voltage_kv: float
    sut_frequency_hz: float
    sut_power_kva: float
    sut_within_limits: bool
    sut_limit_kv: float
    critical_status: bool = False
    status_message: str = "OK"


class InducedVoltageCalculationResult(BaseModel):
    """Individual calculation result for induced voltage."""
    freq_hz: float
    inducao: float
    pot_magnetica: float
    pot_ativa: float
    pot_induzida: float
    pcap: float
    scap_sind_ratio: float
    sut_analysis: InducedVoltageSutAnalysis
    status_validation: StatusValidation


class InducedVoltageResult(BaseTestResult):
    """Complete induced voltage module serialization."""
    module_name: str = "inducedVoltage"

    # Form data
    form_data: InducedVoltageTestParams

    # Calculation results
    calculation_results: List[InducedVoltageCalculationResult] = Field(default_factory=list)

    # Summary results
    summary: Dict[str, Any] = Field(default_factory=dict)

    # Limits and validation
    limites_info: LimitsInfo

    # Critical validations
    eps_estimate_exceeded: bool = False
    sut_voltage_exceeded: bool = False
    frequency_out_of_range: bool = False


# --- Applied Voltage Module Serialization ---

class AppliedVoltageTestParams(BaseModel):
    """Test parameters for applied voltage testing."""
    cap_at_pf: Optional[float] = None
    cap_bt_pf: Optional[float] = None
    cap_ter_pf: Optional[float] = None
    tensao_at_kv: Optional[float] = None
    tensao_bt_kv: Optional[float] = None
    tensao_ter_kv: Optional[float] = None
    frequencia_hz: float = 60.0


class AppliedVoltageResonantConfig(BaseModel):
    """Resonant system configuration for applied voltage."""
    nome: str
    tensao_max: float
    cap_min: float
    cap_max: float
    frequencia: float
    viable: bool = True
    selected: bool = False


class AppliedVoltageWindingResult(BaseModel):
    """Results for individual winding in applied voltage test."""
    winding_name: str  # AT, BT, TER
    capacitancia_original_pf: float
    capacitancia_ajustada_pf: float
    tensao_teste_kv: float
    corrente_ma: float
    potencia_reativa_kvar: float
    impedancia_ohm: float
    resonant_config: Optional[AppliedVoltageResonantConfig] = None
    critical_status: bool = False
    status_message: str = "OK"


class AppliedVoltageResult(BaseTestResult):
    """Complete applied voltage module serialization."""
    module_name: str = "appliedVoltage"

    # Form data
    form_data: AppliedVoltageTestParams

    # Winding results
    winding_results: List[AppliedVoltageWindingResult] = Field(default_factory=list)

    # Available resonant configurations
    available_configs: List[AppliedVoltageResonantConfig] = Field(default_factory=list)

    # Summary calculations
    summary: Dict[str, Any] = Field(default_factory=dict)

    # Limits and validation
    limites_info: LimitsInfo

    # Critical validations
    voltage_exceeded: bool = False
    capacitance_out_of_range: bool = False
    no_viable_config: bool = False


# --- Serialization Utilities ---

class TestDataSerializer:
    """Utility class for serializing and deserializing test data."""

    @staticmethod
    def serialize_to_dict(test_result: BaseTestResult) -> Dict[str, Any]:
        """Serialize test result to dictionary for database storage."""
        return test_result.dict()

    @staticmethod
    def serialize_to_json(test_result: BaseTestResult) -> str:
        """Serialize test result to JSON string."""
        return test_result.json()

    @staticmethod
    def deserialize_from_dict(data: Dict[str, Any], result_type: type) -> BaseTestResult:
        """Deserialize test result from dictionary."""
        return result_type(**data)

    @staticmethod
    def deserialize_from_json(json_str: str, result_type: type) -> BaseTestResult:
        """Deserialize test result from JSON string."""
        return result_type.parse_raw(json_str)

    @staticmethod
    def prepare_for_database(test_result: BaseTestResult) -> Dict[str, Any]:
        """Prepare test result for database storage with proper field mapping."""
        serialized = test_result.dict()

        # Add metadata for database compatibility
        db_data = {
            "module_name": serialized["module_name"],
            "data": json.dumps(serialized),
            "last_updated": serialized["calculation_timestamp"],
            "version": serialized["version"],
            "status": serialized["status"],
            "has_critical_issues": len(serialized.get("critical_issues", [])) > 0,
            "has_alerts": len(serialized.get("alerts", [])) > 0
        }

        return db_data

    @staticmethod
    def extract_from_database(db_record: Dict[str, Any], result_type: type) -> BaseTestResult:
        """Extract test result from database record."""
        data = json.loads(db_record["data"])
        return result_type(**data)


# --- History Integration Models ---

class HistorySessionData(BaseModel):
    """Complete session data for history storage."""
    session_id: str
    session_name: str = ""
    description: str = ""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None

    # All module data
    transformer_inputs: Dict[str, Any] = Field(default_factory=dict)
    losses_data: Optional[LossesResult] = None
    induced_voltage_data: Optional[InducedVoltageResult] = None
    applied_voltage_data: Optional[AppliedVoltageResult] = None

    # Additional modules (for future expansion)
    impulse_data: Optional[Dict[str, Any]] = None
    temperature_rise_data: Optional[Dict[str, Any]] = None
    short_circuit_data: Optional[Dict[str, Any]] = None
    dielectric_analysis_data: Optional[Dict[str, Any]] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# --- Report Generation Models ---

class ReportData(BaseModel):
    """Data structure for PDF/Excel report generation."""
    report_type: str  # "pdf" or "excel"
    selected_modules: List[str]
    session_data: HistorySessionData
    generation_timestamp: datetime = Field(default_factory=datetime.now)

    # Report configuration
    include_detailed_calculations: bool = True
    include_bank_configurations: bool = True
    include_status_validations: bool = True
    include_critical_alerts: bool = True

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


# --- Module-Specific Validation Functions ---

def validate_losses_data(data: Dict[str, Any]) -> List[str]:
    """Validate losses module data integrity."""
    errors = []

    if not data.get("cenarios_detalhados_por_tap"):
        errors.append("Missing detailed scenarios data")

    if not data.get("limites_info"):
        errors.append("Missing limits information")

    # Validate each scenario has required fields
    for tap_data in data.get("cenarios_detalhados_por_tap", []):
        for scenario in tap_data.get("cenarios_do_tap", []):
            if not scenario.get("test_params_cenario"):
                errors.append(f"Missing test parameters for scenario {scenario.get('nome_cenario_teste', 'unknown')}")

    return errors


def validate_induced_voltage_data(data: Dict[str, Any]) -> List[str]:
    """Validate induced voltage module data integrity."""
    errors = []

    if not data.get("form_data"):
        errors.append("Missing form data")

    if not data.get("calculation_results"):
        errors.append("Missing calculation results")

    # Validate required form fields
    form_data = data.get("form_data", {})
    required_fields = ["freq_teste", "capacitancia", "tipo_transformador"]
    for field in required_fields:
        if field not in form_data:
            errors.append(f"Missing required field: {field}")

    return errors


def validate_applied_voltage_data(data: Dict[str, Any]) -> List[str]:
    """Validate applied voltage module data integrity."""
    errors = []

    if not data.get("form_data"):
        errors.append("Missing form data")

    if not data.get("winding_results"):
        errors.append("Missing winding results")

    # Validate at least one winding has data
    winding_results = data.get("winding_results", [])
    if not any(result.get("capacitancia_original_pf") for result in winding_results):
        errors.append("No valid winding capacitance data found")

    return errors
