# utils/constants.py
"""
Centralizes numerical and textual constants used in the application,
especially normative values and physical/component parameters.
"""
import numpy as np

# --- General Constants ---
EPSILON = 1e-6  # Tolerance for floating point comparisons
PI = 3.***************
MU_0 = 4 * np.pi * 1e-7
SQRT_3 = 1.***************

# --- Factors and Limits ---
FACTOR_CAP_BANC_OVERVOLTAGE = 1.1 # Overvoltage factor for determining the V> bank (test voltage higher than bank voltage)

# --- Q Switch Power Configuration for a generic capacitor ---
# All CPxAy capacitors use these powers for their Q1 to Q5 switches
Q_SWITCH_POWERS = {"generic_cp": [0.1, 0.2, 0.8, 1.2, 1.6]}

# Maximum total power that a SINGLE capacitor unit can provide
# (summing all Q switches from Q1 to Q5 for a "generic_cp")
Q_PER_CAPACITOR_UNIT_MVAr = sum(Q_SWITCH_POWERS["generic_cp"]) # 0.1 + 0.2 + 0.8 + 1.2 + 1.6 = 3.9 MVAr

# --- Normative and Test Constants (IEC 60060-1:2010) ---
LIGHTNING_IMPULSE_FRONT_TIME_NOM = 1.2
LIGHTNING_IMPULSE_TAIL_TIME_NOM = 50.0
SWITCHING_IMPULSE_PEAK_TIME_NOM = 250.0
SWITCHING_IMPULSE_TAIL_TIME_NOM = 2500.0
CHOPPED_IMPULSE_CHOP_TIME_MIN = 2.0
CHOPPED_IMPULSE_CHOP_TIME_MAX = 6.0

LIGHTNING_FRONT_TOLERANCE = 0.30
LIGHTNING_TAIL_TOLERANCE = 0.20
LIGHTNING_PEAK_TOLERANCE = 0.03
LIGHTNING_OVERSHOOT_MAX = 10.0

SWITCHING_PEAK_TOLERANCE = 0.03
SWITCHING_PEAK_TIME_TOLERANCE = 0.20
SWITCHING_TAIL_TOLERANCE = 0.60
SWITCHING_TIME_ABOVE_90_MIN = 200.0
SWITCHING_TIME_TO_ZERO_MIN = 1000.0
CHOPPED_IMPULSE_OVERVOLTAGE_FACTOR = 0.2

CHOPPED_FRONT_TOLERANCE = 0.30
CHOPPED_PEAK_TOLERANCE = 0.05
CHOPPED_UNDERSHOOT_MAX = 0.20

VOLTAGE_MAGNITUDE_TOLERANCE = 0.03
SWITCHING_FRONT_TOLERANCE = 0.20
CHOPPED_IMPULSE_CUT_TIME_NOM = 3.0
LI_ALPHA_DEFAULT = 1.2
LI_BETA_DEFAULT = 50.0

# --- Physical and Component Parameters ---
L_PER_STAGE_H = 5e-6
C_PER_STAGE_F = 1.5e-6
C_DIVIDER_HIGH_VOLTAGE_F = 600e-12
C_DIVIDER_LOW_VOLTAGE_F = 1200e-12
C_CHOPPING_GAP_F = 600e-12
R_PARASITIC_OHM = 5.0

# Dielectric strength of materials (kV/mm)
RIGIDEZ_OLEO_MINERAL = 12.5
RIGIDEZ_PAPEL_IMPREGNADO = 50.0
RIGIDEZ_AR_NIVEL_MAR = 3.0
RIGIDEZ_SF6 = 7.5
ALTITUDE_CONST = 8150

# Constants for Induced Voltage
INDUCACAO_LIMITE = 1.9

# Constants for Temperature Analyses
CALOR_ESPECIFICO_OLEO = 1.880
CALOR_ESPECIFICO_COBRE = 0.385
CALOR_ESPECIFICO_FERRO = 0.450
TEMP_AMBIENTE_REFERENCIA = 20
TEMP_RISE_CONSTANT = {"cobre": 234.5, "aluminio": 225.0}

DEFAULT_AMBIENT_TEMP = 25.0
DEFAULT_DELTA_THETA_OIL_MAX = 55.0
DEFAULT_WINDING_MATERIAL = "cobre"
DEFAULT_WINDING_RES_COLD = 0.5
DEFAULT_WINDING_TEMP_COLD = 20.0
DEFAULT_WINDING_RES_HOT = 0.6
DEFAULT_WINDING_TEMP_TOP_OIL = 75.0

# Correction factors for H110-27 steel
FATOR_CONSTRUCAO_PERDAS_H110_27 = 1.15
FATOR_CONSTRUCAO_POTENCIA_MAG_H110_27 = 1.2

# Default excitation factors
FATOR_EXCITACAO_DEFAULT_TRIFASICO = 3
FATOR_EXCITACAO_DEFAULT_MONOFASICO = 5

# Calculation factors for insulation
NEUTRO_NBI_FACTOR = 0.6
SIL_NBI_FACTOR = 0.75
IAC_NBI_FACTOR = 1.1
TENSAO_INDUZIDA_FACTOR = 2.0

# Limits for SIL and PD application
SIL_MIN_VOLTAGE_IEEE = 161.0
SIL_MIN_VOLTAGE_IEC = 245.0
PD_MIN_VOLTAGE = 72.5

# ABNT NBR 5356-1 Tolerances (Losses and Impedance)
LOSSES_TOLERANCE_TOTAL = 0.10
LOSSES_TOLERANCE_INDIVIDUAL = 0.15
IMPEDANCE_TOLERANCE = 0.075

# --- Data Tables (Consolidated) ---
BIL_NBR_IEC = {
    1.2: [], 3.6: [20, 40], 7.2: [40, 60], 12: [60, 75, 95], 17.5: [95, 110], 24: [95, 125, 145],
    36: [145, 170, 200], 52: [250], 72.5: [325, 350], 145: [450, 550, 650], 170: [550, 650, 750],
    245: [750, 850, 950, 1050], 362: [950, 1050, 1175], 420: [1050, 1175, 1300, 1425],
    525: [1300, 1425, 1550], 800: [1800, 1950, 2100],
}
BIL_IEEE = {
    1.2: [30], 5.0: [60], 8.7: [75], 15: [95, 110], 25.8: [125, 150], 34.5: [150, 200],
    46: [200, 250], 69: [250, 350], 161: [550, 650, 750], 230: [650, 750, 825, 900],
    345: [900, 1050, 1175], 500: [1300, 1425, 1550, 1675, 1800], 765: [1800, 1925, 2050],
}
SIL_NBR_IEC = {
    245: [650, 750, 850], 362: [850, 950], 420: [850, 950, 1050, 1175], 525: [1050, 1175], 800: [1425, 1550],
}
SIL_IEEE = {
    161: [460, 540, 620], 230: [540, 620, 685, 745], 345: [745, 870, 975],
    500: [1080, 1180, 1290, 1390, 1500], 765: [1500, 1600, 1700],
}

potencia_magnet_data_M4 = {
    (0.5, 50): 0.10, (0.5, 60): 0.15, (0.5, 100): 0.35, (0.5, 120): 0.45, (0.5, 150): 0.70, (0.5, 200): 1.00, (0.5, 240): 1.30, (0.5, 250): 1.40, (0.5, 300): 1.70, (0.5, 350): 2.10, (0.5, 400): 3.00, (0.5, 500): 4.00,
    (0.6, 50): 0.15, (0.6, 60): 0.20, (0.6, 100): 0.45, (0.6, 120): 0.60, (0.6, 150): 0.90, (0.6, 200): 1.40, (0.6, 240): 1.80, (0.6, 250): 1.90, (0.6, 300): 2.50, (0.6, 350): 3.30, (0.6, 400): 4.00, (0.6, 500): 5.50,
    (0.7, 50): 0.23, (0.7, 60): 0.28, (0.7, 100): 0.60, (0.7, 120): 0.80, (0.7, 150): 1.10, (0.7, 200): 1.70, (0.7, 240): 2.30, (0.7, 250): 2.50, (0.7, 300): 3.40, (0.7, 350): 4.20, (0.7, 400): 5.20, (0.7, 500): 7.50,
    (0.8, 50): 0.30, (0.8, 60): 0.35, (0.8, 100): 0.80, (0.8, 120): 1.00, (0.8, 150): 1.40, (0.8, 200): 2.20, (0.8, 240): 3.00, (0.8, 250): 3.30, (0.8, 300): 4.50, (0.8, 350): 5.50, (0.8, 400): 7.00, (0.8, 500): 9.50,
    (0.9, 50): 0.38, (0.9, 60): 0.45, (0.9, 100): 0.95, (0.9, 120): 1.30, (0.9, 150): 1.70, (0.9, 200): 2.80, (0.9, 240): 3.80, (0.9, 250): 4.00, (0.9, 300): 5.60, (0.9, 350): 7.00, (0.9, 400): 8.80, (0.9, 500): 12.00,
    (1.0, 50): 0.45, (1.0, 60): 0.55, (1.0, 100): 1.10, (1.0, 120): 1.60, (1.0, 150): 2.20, (1.0, 200): 3.50, (1.0, 240): 4.50, (1.0, 250): 4.80, (1.0, 300): 6.90, (1.0, 350): 8.50, (1.0, 400): 11.00, (1.0, 500): 15.00,
    (1.1, 50): 0.55, (1.1, 60): 0.70, (1.1, 100): 1.50, (1.1, 120): 2.00, (1.1, 150): 2.80, (1.1, 200): 4.10, (1.1, 240): 5.50, (1.1, 250): 5.80, (1.1, 300): 8.10, (1.1, 350): 10.00, (1.1, 400): 13.00, (1.1, 500): 18.00,
    (1.2, 50): 0.65, (1.2, 60): 0.85, (1.2, 100): 2.00, (1.2, 120): 2.40, (1.2, 150): 3.30, (1.2, 200): 5.00, (1.2, 240): 6.50, (1.2, 250): 7.00, (1.2, 300): 9.50, (1.2, 350): 12.00, (1.2, 400): 15.00, (1.2, 500): 22.00,
    (1.3, 50): 0.80, (1.3, 60): 1.00, (1.3, 100): 2.20, (1.3, 120): 2.85, (1.3, 150): 3.80, (1.3, 200): 6.00, (1.3, 240): 7.50, (1.3, 250): 8.00, (1.3, 300): 11.20, (1.3, 350): 13.50, (1.3, 400): 17.00, (1.3, 500): 26.00,
    (1.4, 50): 0.95, (1.4, 60): 1.20, (1.4, 100): 2.50, (1.4, 120): 3.30, (1.4, 150): 4.50, (1.4, 200): 7.00, (1.4, 240): 9.00, (1.4, 250): 9.90, (1.4, 300): 13.50, (1.4, 350): 16.00, (1.4, 400): 20.00, (1.4, 500): 30.00,
    (1.5, 50): 1.10, (1.5, 60): 1.40, (1.5, 100): 3.00, (1.5, 120): 4.00, (1.5, 150): 5.50, (1.5, 200): 9.00, (1.5, 240): 11.00, (1.5, 250): 12.00, (1.5, 300): 15.50, (1.5, 350): 18.00, (1.5, 400): 24.00, (1.5, 500): 37.00,
    (1.6, 50): 1.30, (1.6, 60): 1.60, (1.6, 100): 3.50, (1.6, 120): 4.80, (1.6, 150): 6.50, (1.6, 200): 12.00, (1.6, 240): 14.00, (1.6, 250): 15.00, (1.6, 300): 18.00, (1.6, 350): 22.00, (1.6, 400): 30.00, (1.6, 500): 45.00,
    (1.7, 50): 1.60, (1.7, 60): 2.00, (1.7, 100): 4.00, (1.7, 120): 5.50, (1.7, 150): 7.00, (1.7, 200): 15.00, (1.7, 240): 17.00, (1.7, 250): 18.00, (1.7, 300): 22.00, (1.7, 350): 28.00, (1.7, 400): 38.00, (1.7, 500): 55.00,
}
perdas_nucleo_data_M4 = {
    (0.5, 50): 0.10, (0.5, 60): 0.13, (0.5, 100): 0.25, (0.5, 120): 0.35, (0.5, 150): 0.50, (0.5, 200): 0.80, (0.5, 240): 1.10, (0.5, 250): 1.15, (0.5, 300): 1.30, (0.5, 350): 1.50, (0.5, 400): 1.70, (0.5, 500): 2.10,
    (0.6, 50): 0.12, (0.6, 60): 0.18, (0.6, 100): 0.38, (0.6, 120): 0.48, (0.6, 150): 0.70, (0.6, 200): 1.10, (0.6, 240): 1.50, (0.6, 250): 1.60, (0.6, 300): 2.00, (0.6, 350): 2.40, (0.6, 400): 2.80, (0.6, 500): 3.50,
    (0.7, 50): 0.15, (0.7, 60): 0.23, (0.7, 100): 0.50, (0.7, 120): 0.62, (0.7, 150): 0.95, (0.7, 200): 1.55, (0.7, 240): 2.10, (0.7, 250): 2.30, (0.7, 300): 3.00, (0.7, 350): 3.60, (0.7, 400): 4.20, (0.7, 500): 5.50,
    (0.8, 50): 0.20, (0.8, 60): 0.30, (0.8, 100): 0.65, (0.8, 120): 0.80, (0.8, 150): 1.20, (0.8, 200): 2.00, (0.8, 240): 2.80, (0.8, 250): 3.00, (0.8, 300): 3.90, (0.8, 350): 4.70, (0.8, 400): 5.50, (0.8, 500): 7.50,
    (0.9, 50): 0.25, (0.9, 60): 0.37, (0.9, 100): 0.82, (0.9, 120): 1.00, (0.9, 150): 1.50, (0.9, 200): 2.50, (0.9, 240): 3.50, (0.9, 250): 3.80, (0.9, 300): 4.80, (0.9, 350): 5.80, (0.9, 400): 6.80, (0.9, 500): 9.00,
    (1.0, 50): 0.32, (1.0, 60): 0.46, (1.0, 100): 1.00, (1.0, 120): 1.25, (1.0, 150): 1.85, (1.0, 200): 3.10, (1.0, 240): 4.20, (1.0, 250): 4.50, (1.0, 300): 5.90, (1.0, 350): 7.00, (1.0, 400): 8.50, (1.0, 500): 11.00,
    (1.1, 50): 0.41, (1.1, 60): 0.55, (1.1, 100): 1.21, (1.1, 120): 1.55, (1.1, 150): 2.20, (1.1, 200): 3.70, (1.1, 240): 5.00, (1.1, 250): 5.40, (1.1, 300): 6.90, (1.1, 350): 8.50, (1.1, 400): 10.00, (1.1, 500): 14.00,
    (1.2, 50): 0.50, (1.2, 60): 0.65, (1.2, 100): 1.41, (1.2, 120): 1.90, (1.2, 150): 2.70, (1.2, 200): 4.50, (1.2, 240): 6.00, (1.2, 250): 6.40, (1.2, 300): 8.10, (1.2, 350): 10.00, (1.2, 400): 12.00, (1.2, 500): 17.00,
    (1.3, 50): 0.60, (1.3, 60): 0.80, (1.3, 100): 1.65, (1.3, 120): 2.30, (1.3, 150): 3.20, (1.3, 200): 5.20, (1.3, 240): 7.00, (1.3, 250): 7.50, (1.3, 300): 9.50, (1.3, 350): 11.50, (1.3, 400): 14.00, (1.3, 500): 20.00,
    (1.4, 50): 0.71, (1.4, 60): 0.95, (1.4, 100): 1.95, (1.4, 120): 2.80, (1.4, 150): 3.80, (1.4, 200): 6.00, (1.4, 240): 8.50, (1.4, 250): 9.00, (1.4, 300): 11.00, (1.4, 350): 13.50, (1.4, 400): 16.00, (1.4, 500): 24.00,
    (1.5, 50): 0.85, (1.5, 60): 1.10, (1.5, 100): 2.30, (1.5, 120): 3.30, (1.5, 150): 4.50, (1.5, 200): 7.00, (1.5, 240): 10.00, (1.5, 250): 10.60, (1.5, 300): 13.00, (1.5, 350): 15.50, (1.5, 400): 19.00, (1.5, 500): 29.00,
    (1.6, 50): 1.00, (1.6, 60): 1.30, (1.6, 100): 2.80, (1.6, 120): 3.80, (1.6, 150): 5.30, (1.6, 200): 8.00, (1.6, 240): 12.00, (1.6, 250): 12.60, (1.6, 300): 15.00, (1.6, 350): 18.00, (1.6, 400): 23.00, (1.6, 500): 35.00,
    (1.7, 50): 1.20, (1.7, 60): 1.55, (1.7, 100): 3.50, (1.7, 120): 4.40, (1.7, 150): 6.00, (1.7, 200): 9.00, (1.7, 240): 15.00, (1.7, 250): 15.60, (1.7, 300): 18.00, (1.7, 350): 22.00, (1.7, 400): 28.00, (1.7, 500): 42.00,
}
perdas_nucleo_data_H110_27 = {
    (1.9, 50): 2.010, (1.8, 50): 1.398, (1.7, 50): 1.052, (1.6, 50): 0.882, (1.5, 50): 0.760, (1.4, 50): 0.658, (1.3, 50): 0.569, (1.2, 50): 0.488, (1.1, 50): 0.414, (1.0, 50): 0.346, (0.9, 50): 0.284, (0.8, 50): 0.228, (0.7, 50): 0.178, (0.6, 50): 0.135, (0.5, 50): 0.097, (0.4, 50): 0.065, (0.3, 50): 0.038, (0.2, 50): 0.018,
    (1.9, 60): 2.595, (1.8, 60): 1.816, (1.7, 60): 1.383, (1.6, 60): 1.165, (1.5, 60): 1.006, (1.4, 60): 0.873, (1.3, 60): 0.755, (1.2, 60): 0.648, (1.1, 60): 0.549, (1.0, 60): 0.459, (0.9, 60): 0.377, (0.8, 60): 0.301, (0.7, 60): 0.236, (0.6, 60): 0.178, (0.5, 60): 0.128, (0.4, 60): 0.086, (0.3, 60): 0.050, (0.2, 60): 0.023,
}
potencia_magnet_data_H110_27 = { # VA/kg
    (1.9, 50): 14.434, (1.8, 50): 3.438, (1.7, 50): 1.661, (1.6, 50): 1.188, (1.5, 50): 0.962,  (1.4, 50): 0.812, (1.3, 50): 0.698, (1.2, 50): 0.602, (1.1, 50): 0.517,  (1.0, 50): 0.441, (0.9, 50): 0.372, (0.8, 50): 0.308, (0.7, 50): 0.250,  (0.6, 50): 0.196, (0.5, 50): 0.147, (0.4, 50): 0.103, (0.3, 50): 0.064,  (0.2, 50): 0.032,
    (1.9, 60): 17.589, (1.8, 60): 4.178, (1.7, 60): 2.070, (1.6, 60): 1.507, (1.5, 60): 1.230,  (1.4, 60): 1.045, (1.3, 60): 0.900, (1.2, 60): 0.777, (1.1, 60): 0.667,  (1.0, 60): 0.568, (0.9, 60): 0.477, (0.8, 60): 0.395, (0.7, 60): 0.319,  (0.6, 60): 0.249, (0.5, 60): 0.186, (0.4, 60): 0.130, (0.3, 60): 0.081,  (0.2, 60): 0.040,
}
# --- SUT/EPS Test Constants (Legacy - mantidos para compatibilidade) ---
SUT_BT_VOLTAGE = 480.0
SUT_BT_POWER = 4000.0
SUT_AT_POWER = 15000.0  # Legacy - usar SUT_LIMITS_BY_TYPE
SUT_AT_MIN_VOLTAGE = 14000.0  # Tap mínimo disponível é 14kV (para geração de taps)
SUT_AT_MIN_APPLICABLE_VOLTAGE = 0.0  # SUT pode aplicar tensões de 0kV usando tap de 14kV (para validação de tensões de teste)
SUT_AT_MAX_VOLTAGE = 140000.0
SUT_AT_STEP_VOLTAGE = 3000.0
SUT_AT_NOMINAL_VOLTAGE_FOR_SEARCH = 14000.0 # Added to resolve Pylance error
SUT_TERC_MIN_VOLTAGE = 3500.0
SUT_TERC_INTERMEDIATE_VOLTAGE = 7000.0
SUT_TERC_MAX_VOLTAGE = 14000.0
EPS_APARENTE_POWER = 1450.0  # Legacy - usar EPS_LIMITS_BY_TYPE
EPS_CURRENT_LIMIT = 2000.0 # Legacy - usar EPS_LIMITS_BY_TYPE
DUT_POWER_LIMIT_TRIFASICO = 1350.0
DUT_POWER_LIMIT_BIFASICO = 837.0
DUT_POWER_LIMIT_MONOFASICO = 483.0
DUT_POWER_LIMIT = 1350.0  # Compatibilidade - usar DUT_POWER_LIMIT_TRIFASICO
EPS_CURRENT_LIMIT_POSITIVE = 2000.0  # Legacy - usar EPS_LIMITS_BY_TYPE
EPS_CURRENT_LIMIT_NEGATIVE = -2000.0  # Legacy - usar EPS_LIMITS_BY_TYPE
CT_CURRENT_LIMIT = 2000.0  # Current Transformer capacity limit (A)

# --- Dynamic SUT/EPS Limits by Transformer Configuration ---
# Limites de potência por tipo para TAP de referência (140kV AT e 14kV TERC)
SUT_AT_POWER_RATIOS_BY_TYPE = {
    "Trifásico": 15.0,   # 15 MVA para 140kV AT
    "Bifásico": 10.0,    # 10 MVA para 140kV AT
    "Monofásico": 5.0    # 5 MVA para 140kV AT
}

SUT_TERC_POWER_RATIOS_BY_TYPE = {
    "Trifásico": 15,    
    "Bifásico": 10,      
    "Monofásico": 5.0  
}

# Tensões de referência para os cálculos de potência
SUT_AT_REFERENCE_VOLTAGE_KV = 140.0
SUT_TERC_REFERENCE_VOLTAGE_KV = 14.0

def calculate_sut_power_for_tap(sut_eps_type: str, tap_voltage_kv: float, sut_winding: str = "AT") -> float:
    """
    Calcula a potência SUT para um tap específico baseado no tipo SUT/EPS e enrolamento.

    Fórmula: P_tap = P_ref * (V_tap / V_ref)

    Args:
        sut_eps_type: "Trifásico", "Bifásico", ou "Monofásico"
        tap_voltage_kv: Tensão do tap em kV
        sut_winding: "AT" (Alta Tensão) ou "TERC" (Terciário)

    Returns:
        Potência em MVA para o tap especificado
    """
    if sut_eps_type not in SUT_AT_POWER_RATIOS_BY_TYPE:
        sut_eps_type = "Trifásico"  # Padrão

    if sut_winding == "TERC":
        # Usar limites do terciário
        power_ref_mva = SUT_TERC_POWER_RATIOS_BY_TYPE[sut_eps_type]
        reference_voltage_kv = SUT_TERC_REFERENCE_VOLTAGE_KV
    else:
        # Usar limites da AT (padrão)
        power_ref_mva = SUT_AT_POWER_RATIOS_BY_TYPE[sut_eps_type]
        reference_voltage_kv = SUT_AT_REFERENCE_VOLTAGE_KV

    power_tap_mva = power_ref_mva * (tap_voltage_kv / reference_voltage_kv)

    return power_tap_mva

# EPS Limits by transformer configuration type
EPS_LIMITS_BY_TYPE = {
    "Trifásico": {
        "apparent_power_kva": 1450.0,
        "current_limit_positive_a": 2000.0,
        "current_limit_negative_a": -2000.0
    },
    "Bifásico": {
        "apparent_power_kva": 866.0,  # 500.0 * SQRT_3 kVA
        "current_limit_positive_a": 1050.0,
        "current_limit_negative_a": -1050.0
    },
    "Monofásico": {
        "apparent_power_kva": 500.0,
        "current_limit_positive_a": 660.0,
        "current_limit_negative_a": -660.0
    }
}

def get_sut_limits_for_tap(sut_eps_type: str, tap_voltage_kv: float, sut_winding: str = "AT") -> dict:
    """
    Retorna os limites SUT para um tap específico.

    Args:
        sut_eps_type: "Trifásico", "Bifásico", ou "Monofásico"
        tap_voltage_kv: Tensão do tap em kV
        sut_winding: "AT" (Alta Tensão) ou "TERC" (Terciário)

    Returns:
        Dict com limites SUT para o tap especificado
    """
    power_mva = calculate_sut_power_for_tap(sut_eps_type, tap_voltage_kv, sut_winding)

    if sut_winding == "TERC":
        reference_voltage_kv = SUT_TERC_REFERENCE_VOLTAGE_KV
        power_ratio = SUT_TERC_POWER_RATIOS_BY_TYPE[sut_eps_type]
    else:
        reference_voltage_kv = SUT_AT_REFERENCE_VOLTAGE_KV
        power_ratio = SUT_AT_POWER_RATIOS_BY_TYPE[sut_eps_type]

    return {
        "power_mva": power_mva,
        "voltage_line_to_line_kv": tap_voltage_kv,
        "voltage_phase_to_neutral_kv": tap_voltage_kv / SQRT_3,
        "sut_eps_type": sut_eps_type,
        "sut_winding": sut_winding,
        "reference_voltage_kv": reference_voltage_kv,
        "power_ratio": power_ratio
    }




# --- Component and Equipment Configurations ---
RESISTORS_LI_FRONT_AVAILABLE = [{"value": 15, "label": "15 Ω"}, {"value": 20, "label": "20 Ω"}, {"value": 40, "label": "40 Ω"}, {"value": 60, "label": "60 Ω"}, {"value": 90, "label": "90 Ω"}, {"value": 140, "label": "140 Ω"}, {"value": 300, "label": "300 Ω"}]
RESISTORS_LI_TAIL_AVAILABLE = [{"value": 100, "label": "100 Ω"}, {"value": 220, "label": "220 Ω"}, {"value": 300, "label": "300 Ω"}, {"value": 600, "label": "600 Ω"}, {"value": 3500, "label": "3.5 kΩ"}, {"value": 5000, "label": "5.0 kΩ"}]
RESISTORS_SI_FRONT_AVAILABLE = [{"value": 440, "label": "440 Ω"}, {"value": 900, "label": "900 Ω"}, {"value": 1000, "label": "1.0 kΩ"}, {"value": 3000, "label": "3.0 kΩ"}, {"value": 6000, "label": "6.0 kΩ"}]
RESISTORS_SI_TAIL_AVAILABLE = [{"value": 1600, "label": "1.6 kΩ"}, {"value": 2200, "label": "2.2 kΩ"}, {"value": 3600, "label": "3.6 kΩ"}, {"value": 4500, "label": "4.5 kΩ"}, {"value": 5000, "label": "5.0 kΩ"}]
INDUCTORS_OPTIONS = [{"value": 0, "label": "Nenhum"}, {"value": 100e-6, "label": "100 µH"}, {"value": 200e-6, "label": "200 µH"}, {"value": 300e-6, "label": "300 µH"}, {"value": 400e-6, "label": "400 µH"}]
SHUNT_OPTIONS = [{"value": 0.01, "label": "0.01 Ω"}, {"value": 0.02, "label": "0.02 Ω"}, {"value": 0.1, "label": "0.1 Ω"}, {"value": 0.5, "label": "0.5 Ω"}, {"value": 1.0, "label": "1.0 Ω"}]
STRAY_CAPACITANCE_OPTIONS_PF = [{"value": 0, "label": "Nenhuma"}, {"value": 200, "label": "200 pF"}, {"value": 400, "label": "400 pF (padrão)"}, {"value": 600, "label": "600 pF"}, {"value": 800, "label": "800 pF"}]
RT_DEFAULT_PER_COLUMN = {"lightning": 100, "switching": 2500, "chopped": 120}

# Generator Configurations (Example Haefely SGWN 360kJ)
GENERATOR_CONFIGURATIONS = [
    {"value": "1S-1P", "label": "1S-1P (200kV / 1.50µF / 30kJ)", "stages": 1, "parallel": 1, "max_voltage_kv": 200, "energy_kj": 30},
    {"value": "2S-1P", "label": "2S-1P (400kV / 0.75µF / 60kJ)", "stages": 2, "parallel": 1, "max_voltage_kv": 400, "energy_kj": 60},
    {"value": "3S-1P", "label": "3S-1P (600kV / 0.50µF / 90kJ)", "stages": 3, "parallel": 1, "max_voltage_kv": 600, "energy_kj": 90},
    {"value": "4S-1P", "label": "4S-1P (800kV / 0.375µF / 120kJ)", "stages": 4, "parallel": 1, "max_voltage_kv": 800, "energy_kj": 120},
    {"value": "5S-1P", "label": "5S-1P (1000kV / 0.30µF / 150kJ)", "stages": 5, "parallel": 1, "max_voltage_kv": 1000, "energy_kj": 150},
    {"value": "6S-1P", "label": "6S-1P (1200kV / 0.25µF / 180kJ)", "stages": 6, "parallel": 1, "max_voltage_kv": 1200, "energy_kj": 180},
    {"value": "7S-1P", "label": "7S-1P (1400kV / 0.214µF / 210kJ)", "stages": 7, "parallel": 1, "max_voltage_kv": 1400, "energy_kj": 210},
    {"value": "8S-1P", "label": "8S-1P (1600kV / 0.188µF / 240kJ)", "stages": 8, "parallel": 1, "max_voltage_kv": 1600, "energy_kj": 240},
    {"value": "9S-1P", "label": "9S-1P (1800kV / 0.167µF / 270kJ)", "stages": 9, "parallel": 1, "max_voltage_kv": 1800, "energy_kj": 270},
    {"value": "10S-1P", "label": "10S-1P (2000kV / 0.15µF / 300kJ)", "stages": 10, "parallel": 1, "max_voltage_kv": 2000, "energy_kj": 300},
    {"value": "11S-1P", "label": "11S-1P (2200kV / 0.136µF / 330kJ)", "stages": 11, "parallel": 1, "max_voltage_kv": 2200, "energy_kj": 330},
    {"value": "12S-1P", "label": "12S-1P (2400kV / 0.125µF / 360kJ)", "stages": 12, "parallel": 1, "max_voltage_kv": 2400, "energy_kj": 360},
    {"value": "1S-2P", "label": "1S-2P (200kV / 3.00µF / 60kJ)", "stages": 1, "parallel": 2, "max_voltage_kv": 200, "energy_kj": 60},
    {"value": "1S-3P", "label": "1S-3P (200kV / 4.50µF / 90kJ)", "stages": 1, "parallel": 3, "max_voltage_kv": 200, "energy_kj": 90},
    {"value": "1S-4P", "label": "1S-4P (200kV / 6.00µF / 120kJ)", "stages": 1, "parallel": 4, "max_voltage_kv": 200, "energy_kj": 120},
    {"value": "1S-12P", "label": "1S-12P (200kV / 18.00µF / 360kJ)", "stages": 1, "parallel": 12, "max_voltage_kv": 200, "energy_kj": 360},
    {"value": "2S-2P", "label": "2S-2P (400kV / 1.50µF / 120kJ)", "stages": 2, "parallel": 2, "max_voltage_kv": 400, "energy_kj": 120},
    {"value": "2S-6P", "label": "2S-6P (400kV / 4.50µF / 360kJ)", "stages": 2, "parallel": 6, "max_voltage_kv": 400, "energy_kj": 360},
    {"value": "3S-2P", "label": "3S-2P (600kV / 1.00µF / 180kJ)", "stages": 3, "parallel": 2, "max_voltage_kv": 600, "energy_kj": 180},
    {"value": "3S-4P", "label": "3S-4P (600kV / 2.00µF / 360kJ)", "stages": 3, "parallel": 4, "max_voltage_kv": 600, "energy_kj": 360},
    {"value": "4S-2P", "label": "4S-2P (800kV / 0.75µF / 240kJ)", "stages": 4, "parallel": 2, "max_voltage_kv": 800, "energy_kj": 240},
    {"value": "4S-3P", "label": "4S-3P (800kV / 1.125µF / 360kJ)", "stages": 4, "parallel": 3, "max_voltage_kv": 800, "energy_kj": 360},
    {"value": "6S-2P", "label": "6S-2P (1200kV / 0.50µF / 360kJ)", "stages": 6, "parallel": 2, "max_voltage_kv": 1200, "energy_kj": 360},
]

# Reference Data for Resistor Suggestion (Haefely-based)
RF_REFERENCE_DATA_LI = [(0.5, 500), (1, 350), (2, 220), (4, 140), (8, 90), (16, 60), (32, 40)]
RF_REFERENCE_DATA_SI = [(0.5, 5000), (1, 3500), (2, 2200), (4, 1400), (8, 900), (16, 600), (32, 400)]

# Resonant System Configurations for Applied Voltage
RESONANT_SYSTEM_CONFIGS = {
    "Módulos 1+2+3 (Série)": {"tensao_max": 1350, "cap_min": 0.22, "cap_max": 2.6},
    "Módulos 1+2 (Série)": {"tensao_max": 900, "cap_min": 0.3, "cap_max": 6.5},
    "Módulo 1 (1 em Par.)": {"tensao_max": 450, "cap_min": 0.7, "cap_max": 13.1},
    "Módulos 1||2||3 (3 Par.) 450kV": {"tensao_max": 450, "cap_min": 2.0, "cap_max": 23.6},
    "Módulos 1||2||3 (3 Par.) 270kV": {"tensao_max": 270, "cap_min": 2.0, "cap_max": 39.3}
}

# --- Mappings and Constants for UI Dropdowns ---
MATERIAL_OPTIONS = [{"label": "Cobre", "value": "cobre"}, {"label": "Alumínio", "value": "aluminio"}, {"label": "Não Aplicável", "value": "na"}]
POWER_CATEGORY_OPTIONS = [{"label": "I (≤ 2.5 MVA)", "value": "I"}, {"label": "II (> 2.5 a 100 MVA)", "value": "II"}, {"label": "III (> 100 MVA)", "value": "III"}]
CONNECTION_OPTIONS = [{"label": "Y (Estrela)", "value": "Y"}, {"label": "YN (Estrela com Neutro)", "value": "YN"}, {"label": "D (Triângulo/Delta)", "value": "D"}, {"label": "Z (Zigue-Zague)", "value": "Z"}, {"label": "ZN (Zigue-Zague com Neutro)", "value": "ZN"}]
ISOLATION_TYPE_OPTIONS = [{"label": "Uniforme", "value": "uniforme"}, {"label": "Progressivo", "value": "progressivo"}, {"label": "Não Aplicável", "value": "na"}]

# --- Vector Groups (Grupos Vetoriais) ---
# Complete list of vector groups organized by transformer type
# Based on IEC 60076-1 standard and practical applications
VECTOR_GROUPS = {
    # Transformadores Trifásicos com Enrolamentos Separados
    "Trifásico": {
        # Ligação Primária em Triângulo (D)
        "primary_delta": {
            # Com Secundário em Triângulo (d) - Defasagens em múltiplos de 60° (números pares)
            "secondary_delta": ["Dd0", "Dd2", "Dd4", "Dd6", "Dd8", "Dd10"],
            # Com Secundário em Estrela (y) - Defasagens em múltiplos de 30° (números ímpares)
            "secondary_star": ["Dy1", "Dyn1", "Dy3", "Dyn3", "Dy5", "Dyn5", "Dy7", "Dyn7", "Dy9", "Dyn9", "Dy11", "Dyn11"],
            # Com Secundário em Ziguezague (z) - Defasagens em múltiplos de 60° (números pares)
            "secondary_zigzag": ["Dz0", "Dzn0", "Dz2", "Dzn2", "Dz4", "Dzn4", "Dz6", "Dzn6", "Dz8", "Dzn8", "Dz10", "Dzn10"]
        },
        # Ligação Primária em Estrela (Y)
        "primary_star": {
            # Com Secundário em Triângulo (d) - Defasagens em múltiplos de 30° (números ímpares)
            "secondary_delta": ["Yd1", "YNd1", "Yd3", "YNd3", "Yd5", "YNd5", "Yd7", "YNd7", "Yd9", "YNd9", "Yd11", "YNd11"],
            # Com Secundário em Estrela (y) - Defasagens em múltiplos de 60° (números pares)
            "secondary_star": ["Yy0", "YNyn0", "Yy2", "YNyn2", "Yy4", "YNyn4", "Yy6", "YNyn6", "Yy8", "YNyn8", "Yy10", "YNyn10"],
            # Com Secundário em Ziguezague (z) - Defasagens em múltiplos de 30° (números ímpares)
            "secondary_zigzag": ["Yz1", "YNzn1", "Yz3", "YNzn3", "Yz5", "YNzn5", "Yz7", "YNzn7", "Yz9", "YNzn9", "Yz11", "YNzn11"]
        },
        # Ligação Primária em Ziguezague (Z)
        "primary_zigzag": {
            # Com Secundário em Triângulo (d) - Defasagens em múltiplos de 60° (números pares)
            "secondary_delta": ["Zd0", "ZNd0", "Zd2", "ZNd2", "Zd4", "ZNd4", "Zd6", "ZNd6", "Zd8", "ZNd8", "Zd10", "ZNd10"],
            # Com Secundário em Estrela (y) - Defasagens em múltiplos de 30° (números ímpares)
            "secondary_star": ["Zy1", "ZNyn1", "Zy3", "ZNyn3", "Zy5", "ZNyn5", "Zy7", "ZNyn7", "Zy9", "ZNyn9", "Zy11", "ZNyn11"],
            # Com Secundário em Ziguezague (z) - Defasagens em múltiplos de 60° (números pares)
            "secondary_zigzag": ["Zz0", "ZNzn0", "Zz2", "ZNzn2", "Zz4", "ZNzn4", "Zz6", "ZNzn6", "Zz8", "ZNzn8", "Zz10", "ZNzn10"]
        },
        # Configurações com Terciário (exemplos mais comuns)
        "with_tertiary": [
            "YNyn0d11", "YNd11yn0", "YNd1y0", "YNd1y2", "YNd1y4", "YNd1y6", "YNd1y8", "YNd1y10",
            "YNd3y0", "YNd3y2", "YNd3y4", "YNd3y6", "YNd3y8", "YNd3y10",
            "YNd5y0", "YNd5y2", "YNd5y4", "YNd5y6", "YNd5y8", "YNd5y10",
            "YNd7y0", "YNd7y2", "YNd7y4", "YNd7y6", "YNd7y8", "YNd7y10",
            "YNd9y0", "YNd9y2", "YNd9y4", "YNd9y6", "YNd9y8", "YNd9y10",
            "YNd11y0", "YNd11y2", "YNd11y4", "YNd11y6", "YNd11y8", "YNd11y10"
        ]
    },
    # Autotransformadores Trifásicos
    "Autotransformador": {
        # Sem Terciário
        "without_tertiary": ["YNa0", "Ya0"],
        # Com Terciário em Triângulo (números ímpares)
        "tertiary_delta": ["YNa0d1", "YNa0d3", "YNa0d5", "YNa0d7", "YNa0d9", "YNa0d11"],
        # Com Terciário em Estrela (números pares)
        "tertiary_star": ["YNa0y0", "YNa0y2", "YNa0y4", "YNa0y6", "YNa0y8", "YNa0y10"],
        # Com Terciário em Ziguezague (números ímpares)
        "tertiary_zigzag": ["YNa0z1", "YNa0z3", "YNa0z5", "YNa0z7", "YNa0z9", "YNa0z11"]
    },
    # Transformadores Monofásicos (Polaridade)
    "Monofásico": {
        # Transformador Monofásico com Enrolamentos Separados
        "separate_windings": {
            "subtractive": ["YNyn0d1", "YNyn0d3 ", "YNyn0d5", "YNyn0d7", "YNyn0d9", "YNyn0d11","II0", "Ii0"],  # Polaridade Subtrativa (defasagem de 0°)
            "additive": ["II6", "Ii6"]     # Polaridade Aditiva (defasagem de 180°)
        },
        # Notações alternativas comuns
        "alternative_notations": ["I_I_0", "I_I_6", "Iio"]
    },
    # Autotransformadores Monofásicos
    "Autotransformador Monofásico": {
        # Sem Terciário
        "without_tertiary": ["Ia0"],
        # Com Terciário Delta (polaridade de cada enrolamento definida individualmente)
        "with_tertiary_delta": ["Yna0d1","Yna0d3", "Yna0d5", "Yna0d7", "Yna0d9", "Yna0d11","Ia0d1", "Ia0d3", "Ia0d5", "Ia0d7", "Ia0d9", "Ia0d11"],
        # Com Terciário Estrela
        "with_tertiary_star": ["Yna0y1", "Ia0y2", "Ia0y4", "Ia0y6", "Ia0y8", "Ia0y10"],
        # Notações alternativas
        "alternative_notations": [ "Ia0i6"]
    }
}

# Function to determine SUT/EPS type based on transformer configuration
def determine_sut_eps_type(transformer_data: dict) -> str:
    """
    Determina o tipo SUT/EPS baseado nos dados do transformador.

    Lógica:
    - Transformador Trifásico sem terciário → SUT/EPS Trifásico
    - Transformador/Autotransformador com terciário Delta (d) → SUT/EPS Bifásico
    - Transformador/Autotransformador Monofásico:
      * Com terciário Estrela (y) ou Ziguezague (z) → SUT/EPS Monofásico
      * Sem terciário → SUT/EPS Monofásico

    Args:
        transformer_data: Dados do transformador (tipo_transformador, grupo_ligacao, etc.)

    Returns:
        "Trifásico", "Bifásico", ou "Monofásico"
    """
    # Validação robusta de entrada
    if not isinstance(transformer_data, dict):
        return "Trifásico"  # Padrão seguro

    tipo_transformador = transformer_data.get("tipo_transformador", "")
    grupo_ligacao = transformer_data.get("grupo_ligacao", "")

    # Tratar valores None, vazios ou inválidos
    if not tipo_transformador or tipo_transformador in ['null', 'undefined', '']:
        return "Trifásico"  # Padrão seguro

    if not grupo_ligacao or grupo_ligacao in ['null', 'undefined', '']:
        grupo_ligacao = ""

    # Normalizar tipo para compatibilidade
    def normalize_type_local(tipo: str) -> str:
        if not tipo:
            return "Trifásico"
        tipo_lower = tipo.lower()
        if "autotransformador" in tipo_lower:
            if "monofásico" in tipo_lower or "monofasico" in tipo_lower:
                return "Monofásico"
            else:
                return "Trifásico"
        if "monofásico" in tipo_lower or "monofasico" in tipo_lower:
            return "Monofásico"
        else:
            return "Trifásico"

    normalized_type = normalize_type_local(tipo_transformador)

    # Analisar grupo de ligação para detectar terciário ANTES de retornar o tipo base
    if grupo_ligacao:
        grupo_lower = grupo_ligacao.lower()

        # Detectar terciário Delta (d) - usar SUT/EPS Bifásico
        # Aplicável tanto para transformadores trifásicos quanto monofásicos
        if "d" in grupo_lower and len(grupo_lower) > 2:
            # Verificar se 'd' não é parte de 'Dyn' ou similar
            # Procurar por padrões como 'a0d1', 'yn0d1', 'yna0d1', etc.
            if ("a0d" in grupo_lower or "yn0d" in grupo_lower or "yna0d" in grupo_lower or
                any(f"d{i}" in grupo_lower for i in [1, 3, 5, 7, 9, 11])):
                return "Bifásico"

        # Para transformadores monofásicos, verificar terciário Estrela/Ziguezague
        if normalized_type == "Monofásico":
            # Detectar terciário Estrela (y) ou Ziguezague (z) - usar SUT/EPS Monofásico
            if ("a0y" in grupo_lower or "a0z" in grupo_lower or
                "yn0y" in grupo_lower or "yn0z" in grupo_lower or
                "yna0y" in grupo_lower or "yna0z" in grupo_lower):
                return "Monofásico"

    # Se chegou aqui, usar o tipo base normalizado
    if normalized_type == "Monofásico":
        return "Monofásico"
    else:
        return "Trifásico"

# Functions to get dynamic limits
def get_eps_limits(transformer_data: dict) -> dict:
    """
    Retorna os limites EPS baseados no tipo de transformador.

    Args:
        transformer_data: Dados do transformador

    Returns:
        Dict com limites EPS (apparent_power_kva, current_limit_positive_a, current_limit_negative_a)
    """
    if not transformer_data:
        # Fallback para dados vazios - usar limites Trifásico como padrão
        return EPS_LIMITS_BY_TYPE["Trifásico"].copy()

    sut_eps_type = determine_sut_eps_type(transformer_data)
    return EPS_LIMITS_BY_TYPE[sut_eps_type].copy()

def get_dut_power_limit(transformer_data: dict) -> float:
    """
    Retorna o limite de potência DUT baseado no tipo de transformador.

    Args:
        transformer_data: Dados do transformador

    Returns:
        Limite de potência DUT em kW
    """
    sut_eps_type = determine_sut_eps_type(transformer_data)

    if sut_eps_type == "Trifásico":
        return DUT_POWER_LIMIT_TRIFASICO
    elif sut_eps_type == "Bifásico":
        return DUT_POWER_LIMIT_BIFASICO
    else:  # Monofásico
        return DUT_POWER_LIMIT_MONOFASICO

def get_sut_limits(transformer_data: dict, tap_voltage_kv: float = None, sut_winding: str = "AT") -> dict:
    """
    Retorna os limites SUT baseados no tipo de transformador e tap específico.

    Args:
        transformer_data: Dados do transformador
        tap_voltage_kv: Tensão do tap em kV (se None, usa tensão de referência)
        sut_winding: "AT" (Alta Tensão) ou "TERC" (Terciário)

    Returns:
        Dict com limites SUT para o tap especificado
    """
    sut_eps_type = determine_sut_eps_type(transformer_data)

    if tap_voltage_kv is None:
        if sut_winding == "TERC":
            tap_voltage_kv = SUT_TERC_REFERENCE_VOLTAGE_KV
        else:
            tap_voltage_kv = SUT_AT_REFERENCE_VOLTAGE_KV

    return get_sut_limits_for_tap(sut_eps_type, tap_voltage_kv, sut_winding)

def get_all_sut_taps_with_limits(transformer_data: dict) -> dict:
    """
    Retorna todos os taps SUT disponíveis (AT e TERC) com seus respectivos limites de potência.

    Args:
        transformer_data: Dados do transformador

    Returns:
        Dict com listas de taps AT e TERC com informações de cada tap
    """
    import numpy as np

    sut_eps_type = determine_sut_eps_type(transformer_data)

    # Gerar taps AT (14kV a 140kV em passos de 3kV)
    at_taps_voltage_kv = []
    for tap_v in np.arange(SUT_AT_MIN_VOLTAGE, SUT_AT_MAX_VOLTAGE + SUT_AT_STEP_VOLTAGE, SUT_AT_STEP_VOLTAGE):
        if tap_v > 0:
            at_taps_voltage_kv.append(tap_v / 1000.0)  # Converter V para kV

    # Gerar taps TERC (3.5kV, 7kV, 14kV)
    terc_taps_voltage_kv = [
        SUT_TERC_MIN_VOLTAGE / 1000.0,         # 3.5kV
        SUT_TERC_INTERMEDIATE_VOLTAGE / 1000.0, # 7kV
        SUT_TERC_MAX_VOLTAGE / 1000.0          # 14kV
    ]

    # Calcular limites para taps AT
    at_taps_with_limits = []
    for tap_kv in at_taps_voltage_kv:
        tap_limits = get_sut_limits_for_tap(sut_eps_type, tap_kv, "AT")
        at_taps_with_limits.append({
            "tap_voltage_kv": tap_kv,
            "power_limit_mva": tap_limits["power_mva"],
            "voltage_line_to_line_kv": tap_limits["voltage_line_to_line_kv"],
            "voltage_phase_to_neutral_kv": tap_limits["voltage_phase_to_neutral_kv"],
            "sut_eps_type": sut_eps_type,
            "sut_winding": "AT"
        })

    # Calcular limites para taps TERC
    terc_taps_with_limits = []
    for tap_kv in terc_taps_voltage_kv:
        tap_limits = get_sut_limits_for_tap(sut_eps_type, tap_kv, "TERC")
        terc_taps_with_limits.append({
            "tap_voltage_kv": tap_kv,
            "power_limit_mva": tap_limits["power_mva"],
            "voltage_line_to_line_kv": tap_limits["voltage_line_to_line_kv"],
            "voltage_phase_to_neutral_kv": tap_limits["voltage_phase_to_neutral_kv"],
            "sut_eps_type": sut_eps_type,
            "sut_winding": "TERC"
        })

    return {
        "at_taps": at_taps_with_limits,
        "terc_taps": terc_taps_with_limits,
        "sut_eps_type": sut_eps_type
    }

# Function to get flattened list of vector groups by transformer type
def get_vector_groups_by_type(transformer_type: str) -> list:
    """
    Returns a flattened list of vector groups for a given transformer type.

    Args:
        transformer_type: "Trifásico", "Autotransformador", or "Monofásico"

    Returns:
        List of vector group strings
    """
    if transformer_type not in VECTOR_GROUPS:
        return []

    groups = []
    type_data = VECTOR_GROUPS[transformer_type]

    if transformer_type == "Trifásico":
        # Flatten all primary-secondary combinations
        for primary_type, secondary_data in type_data.items():
            if primary_type == "with_tertiary":
                groups.extend(secondary_data)
            else:
                for _, group_list in secondary_data.items():
                    groups.extend(group_list)

    elif transformer_type in ["Autotransformador", "Autotransformador Monofásico"]:
        # Flatten all autotransformer configurations
        for _, group_list in type_data.items():
            groups.extend(group_list)

    elif transformer_type == "Monofásico":
        # Flatten all monophase configurations
        for _, config_data in type_data.items():
            if isinstance(config_data, dict):
                for _, group_list in config_data.items():
                    groups.extend(group_list)
            else:
                groups.extend(config_data)

    # Remove duplicates while preserving order
    seen = set()
    unique_groups = []
    for group in groups:
        if group not in seen:
            seen.add(group)
            unique_groups.append(group)

    return unique_groups

# Common vector groups for quick access (most frequently used)
COMMON_VECTOR_GROUPS = {
    "Trifásico": ["Dyn1", "Dyn11", "Dy5", "Dy11", "Yyn0", "Ynd11", "YNyn0", "YNd11", "Dd0", "Dd6"],
    "Autotransformador": ["YNa0", "YNa0d11"],
    "Monofásico": ["II0", "Ii0", "II6"],
    "Autotransformador Monofásico": ["Ia0"]
}

TRANSFORMER_TYPE_OPTIONS = [
    {"label": "Transformador Trifásico", "value": "Trifásico"},
    {"label": "Transformador Monofásico", "value": "Monofásico"},
    {"label": "Autotransformador Trifásico", "value": "Autotransformador"},
    {"label": "Autotransformador Monofásico", "value": "Autotransformador Monofásico"}
]

# Legacy transformer type options (mantido para compatibilidade)
LEGACY_TRANSFORMER_TYPE_OPTIONS = [
    {"label": "Monofásico", "value": "monofasico"}, {"label": "Trifásico", "value": "trifasico"},
    {"label": "Trifásico com Terciário", "value": "trifasico_terciario"}, {"label": "Trifásico com Terciário e Neutro", "value": "trifasico_terciario_neutro"},
    {"label": "Trifásico com Neutro", "value": "trifasico_neutro"}, {"label": "Trifásico com Neutro e Terciário", "value": "trifasico_neutro_terciario"},
    {"label": "Trifásico com Terciário e Neutro (Zigue-Zague)", "value": "trifasico_terciario_neutro_zigzag"},
    {"label": "Trifásico com Neutro (Zigue-Zigue)", "value": "trifasico_neutro_zigzag"},
    {"label": "Trifásico com Terciário (Zigue-Zague)", "value": "trifasico_terciario_zigzag"},
    {"label": "Trifásico (Zigue-Zague)", "value": "trifasico_zigzag"}, {"label": "Monofásico (Zigue-Zague)", "value": "monofasico_zigzag"},
    {"label": "Não Aplicável", "value": "na"}
]

DEFAULT_ACSD_VALUES = { # Default applied voltage (ACSD) values based on voltage class
    1.1: 3, 3.6: 10, 7.2: 20, 12: 28, 24: 50, 36: 70, 52: 95, 72.5: 140,
    100: 185, 123: 230, 145: 275, 170: 325, 245: 460, 300: 570, 362: 680,
    420: 800, 550: 970
}
DEFAULT_FREQUENCY = 60

# --- Specific Constants for Monophase Balancing Logic ---

# Monophase voltages specifically configured in the EPS system (subset of EPS_CAP_BANK_VOLTAGES_KV)
MONOPHASE_VOLTAGES = ["13.8", "27.6", "41.4", "55.2"]

# Limits for Reactive Power by Voltage Level (Consolidated from C# and Python)
# Format: "voltage_kv": {"grupo1": {"min": MVAr, "max": MVAr}, "grupo1_2": {"min": MVAr, "max": MVAr}}
CAPACITOR_POWER_LIMITS_BY_VOLTAGE = {
    # 13.8 kV (Nominal single unit voltage, 1 unit in series string)
    "13.8": {"grupo1": {"min": 0.3, "max": 11.7}, "grupo1_2": {"min": 0.6, "max": 23.4}},
    # 23.9 kV (Nominal single unit voltage, 1 unit in series string)
    "23.9": {"grupo1": {"min": 0.3, "max": 11.7}, "grupo1_2": {"min": 0.6, "max": 23.4}},
    # 27.6 kV (2 units in series string)
    "27.6": {"grupo1": {"min": 1.2, "max": 46.8}, "grupo1_2": {"min": 2.4, "max": 93.6}},
    # 41.4 kV (3 units in series string)
    "41.4": {"grupo1": {"min": 0.9, "max": 35.1}, "grupo1_2": {"min": 1.8, "max": 70.2}},
    # 47.8 kV (4 units in series string)
    "47.8": {"grupo1": {"min": 1.2, "max": 46.8}, "grupo1_2": {"min": 2.4, "max": 93.6}},
    # 55.2 kV (4 units in series string) - 24 units total, 12 units in Group 1 (same as 47.8kV and 95.6kV)
    "55.2": {"grupo1": {"min": 1.2, "max": 46.8}, "grupo1_2": {"min": 2.4, "max": 93.6}},
    # 71.7 kV (Assumed to use CP1-CP4, similar to 47.8/55.2, or might need more units based on hardware)
    "71.7": {"grupo1": {"min": 0.9, "max": 35.1}, "grupo1_2": {"min": 1.8, "max": 70.2}}, # Re-evaluating based on CPxAy units available
    # 95.6 kV (Assumed to use CP1-CP4, similar to 47.8/55.2, or might need more units based on hardware)
    "95.6": {"grupo1": {"min": 1.2, "max": 46.8}, "grupo1_2": {"min": 2.4, "max": 93.6}}  # Re-evaluating based on CPxAy units available
}

# Nominal Voltages for EPS Capacitor Banks (kV)
# These are the discrete voltage levels the bank can operate at.
EPS_CAP_BANK_VOLTAGES_KV = [13.8, 23.9, 27.6, 41.4, 47.8, 55.2, 71.7, 95.6]

# Number of individual capacitor units (CPxAy) required to form ONE series string
# at a given bank nominal voltage. This determines the 'x' positions that must be present.
# Based on the common structure where CP1, CP2, CP3, CP4 form series connections.
# Example: For 27.6kV, 2 units (CP1 and CP2) are in series.
UNITS_PER_SERIES_STRING_BY_VOLTAGE_KV = {
    
    13.8: 1,  
    23.9: 1, 
    27.6: 2,
    41.4: 3,
    47.8: 2,
    55.2: 4,
    71.7: 3,                
    95.6: 4   
}



# Capacitor units by voltage for three-phase transformers
CAPACITORS_BY_VOLTAGE_TRI = {
    "13.8": ["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"],
    "23.9": ["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"],
    "27.6": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "41.4": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "47.8": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "55.2": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "71.7": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "95.6": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
}

# Capacitor units by voltage for single-phase transformers
CAPACITORS_BY_VOLTAGE_MONO = {
    "13.8": ["CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2"],
    "27.6": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "41.4": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
    "55.2": ["CP1A1", "CP1A2", "CP1B1", "CP1B2", "CP1C1", "CP1C2", "CP2A1", "CP2A2", "CP2B1", "CP2B2", "CP2C1", "CP2C2", "CP3A1", "CP3A2", "CP3B1", "CP3B2", "CP3C1", "CP3C2", "CP4A1", "CP4A2", "CP4B1", "CP4B2", "CP4C1", "CP4C2"],
}

# CS switches by voltage for three-phase transformers
CS_SWITCHES_BY_VOLTAGE_TRI = {
    "13.8": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS7A", "CS7B", "CS7C"],
    "23.9": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS6A", "CS6B"],
    "27.6": ["CSA", "CSB", "CSC", "CS2A1", "CS2A2", "CS2B1", "CS2B2", "CS2C1", "CS2C2", "CS3A1", "CS3A2", "CS3B1", "CS3B2", "CS3C1", "CS3C2", "CS7A", "CS7B", "CS7C"],
    "41.4": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS4A1", "CS4A2", "CS1B1", "CS1B2", "CS4B1", "CS4B2", "CS1C1", "CS1C2", "CS4C1", "CS4C2", "CS7A", "CS7B", "CS7C"],
    "47.8": ["CSA", "CSB", "CSC", "CS2A1", "CS2A2", "CS2B1", "CS2B2", "CS2C1", "CS2C2", "CS3A1", "CS3A2", "CS3B1", "CS3B2", "CS3C1", "CS3C2", "CS6A", "CS6B"],
    "55.2": ["CSA", "CSB", "CSC", "CS4A1", "CS4A2", "CS4B1", "CS4B2", "CS4C1", "CS4C2", "CS7A", "CS7B", "CS7C"],
    "71.7": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS4A1", "CS4A2", "CS1B1", "CS1B2", "CS4B1", "CS4B2", "CS1C1", "CS1C2", "CS4C1", "CS4C2", "CS6A", "CS6B"],
    "95.6": ["CSA", "CSB", "CSC", "CS4A1", "CS4A2", "CS4B1", "CS4B2", "CS4C1", "CS4C2", "CS6A", "CS6B"],
}

# CS switches by voltage for single-phase transformers
CS_SWITCHES_BY_VOLTAGE_MONO = {
    "13.8": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS2A1", "CS2A2", "CS1B1", "CS1B2", "CS2B1", "CS2B2", "CS1C1", "CS1C2", "CS2C1", "CS2C2", "CS6A", "CS6B", "CS6C"],
    "27.6": ["CSA", "CSB", "CSC", "CS2A1", "CS2A2", "CS2B1", "CS2B2", "CS2C1", "CS2C2", "CS3A1", "CS3A2", "CS3B1", "CS3B2", "CS3C1", "CS3C2", "CS6A", "CS6B", "CS6C"],
    "41.4": ["CSA", "CSB", "CSC", "CS1A1", "CS1A2", "CS4A1", "CS4A2", "CS1B1", "CS1B2", "CS4B1", "CS4B2", "CS1C1", "CS1C2", "CS4C1", "CS4C2", "CS6A", "CS6B", "CS6C"],
    "55.2": ["CSA", "CSB", "CSC", "CS4A1", "CS4A2", "CS4B1", "CS4B2", "CS4C1", "CS4C2", "CS6A", "CS6B", "CS6C"],
}