// public/scripts/induced_voltage.js - ATUALIZADO

import { loadAndPopulateTransformerInfo } from './common_module.js';
import { fillFormWithData, waitForApiSystem, collectFormData } from './api_persistence.js';

// Função para carregar dados do store 'inducedVoltage' e preencher o formulário
async function loadInducedVoltageDataAndPopulateForm() {
    try {
        console.log('[induced_voltage] Carregando dados do store "inducedVoltage" e preenchendo formulário...');
        await waitForApiSystem(); // Garante que o sistema de persistência esteja pronto

        const store = window.apiDataSystem.getStore('inducedVoltage');
        const data = await store.getData();

        if (data && data.formData && Object.keys(data.formData).length > 0) {
            const formElement = document.getElementById('induced-voltage-form');
            if (formElement) {
                // Filtrar dados para não aplicar valores padrão indesejados
                const filteredData = {};
                Object.keys(data.formData).forEach(key => {
                    const value = data.formData[key];
                    // Só aplica o valor se não for um valor padrão indesejado
                    // Removido 'Trifásico' da filtragem pois agora é um campo válido independente
                    // Removido '120' da filtragem pois é um valor válido para frequência de teste
                    if (value !== '1500' && value !== '' && value != null) {
                        filteredData[key] = value;
                    }
                });

                console.log('[induced_voltage] Dados filtrados para aplicar:', filteredData);

                if (Object.keys(filteredData).length > 0) {
                    fillFormWithData(formElement, filteredData);
                    console.log('[induced_voltage] Formulário de tensão induzida preenchido com dados filtrados:', filteredData);
                } else {
                    console.log('[induced_voltage] Nenhum dado válido para preencher após filtragem.');
                }
            } else {
                console.warn('[induced_voltage] Formulário "induced-voltage-form" não encontrado para preenchimento.');
            }
        } else {
            console.log('[induced_voltage] Nenhum dado de tensão induzida encontrado no store.');
        }

        // Carregar e exibir resultados salvos se existirem
        if (data && data.calculationResults) {
            console.log('[induced_voltage] Carregando resultados salvos:', data.calculationResults);
            await displayInducedVoltageResults(data.calculationResults);
        }
    } catch (error) {
        console.error('[induced_voltage] Erro ao carregar e preencher dados de tensão induzida:', error);
    }
}

// Função para preencher alguns inputs com dados do transformer_inputs
async function fillInputsFromTransformerData() {
    // Dados automáticos agora são exibidos diretamente na tabela de resultados
    console.log('[induced_voltage] Dados automáticos integrados aos Parâmetros de Entrada');
}

// Função para salvar resultados de cálculo
async function saveCalculationResults(results) {
    try {
        if (window.apiDataSystem) {
            const inducedVoltageStore = window.apiDataSystem.getStore('inducedVoltage');
            const currentStoreData = await inducedVoltageStore.getData() || {};
            const newFormData = collectFormData(document.getElementById('induced-voltage-form'));

            // Preservar dados existentes e adicionar novos resultados
            const updatedStoreData = {
                calculationResults: results,
                formData: { ...(currentStoreData.formData || {}), ...newFormData },
                timestamp: new Date().toISOString()
            };

            await inducedVoltageStore.updateData(updatedStoreData);
            console.log('[induced_voltage] Resultados salvos no store:', updatedStoreData);
        }
    } catch (error) {
        console.error('[induced_voltage] Erro ao salvar resultados:', error);
    }
}

// Função para resetar resultados de cálculos de tensão induzida
function resetInducedVoltageResults() {
    console.log('[induced_voltage] resetInducedVoltageResults: Resetando resultados de cálculos');

    try {
        // Limpar área de resultados
        const resultsContainer = document.getElementById('resultado-tensao-induzida');
        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="text-muted text-center py-3">Aguardando cálculo...</div>';
        }

        // Limpar mensagem de erro
        const errorElement = document.getElementById('induced-voltage-error-message');
        if (errorElement) {
            errorElement.textContent = '';
        }

        // Limpar tabela de frequências se existir
        const freqTableContainer = document.getElementById('frequency-table-container');
        if (freqTableContainer) {
            freqTableContainer.innerHTML = '';
        }

        console.log('[induced_voltage] Resultados de tensão induzida resetados');

    } catch (error) {
        console.error('[induced_voltage] Erro ao resetar resultados:', error);
    }
}

// Lógica para o botão "Calcular"
async function setupCalcButton() {
    const calcBtn = document.getElementById('calc-induced-voltage-btn');
    if (calcBtn) {
        calcBtn.addEventListener('click', async function() {
            console.log('🔄 [induced_voltage] Botão Calcular Tensão Induzida clicado!');

            // Limpar mensagens anteriores
            const resultsDiv = document.getElementById('resultado-tensao-induzida');
            const errorDiv = document.getElementById('induced-voltage-error-message');
            errorDiv.textContent = '';
            resultsDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Calculando...</div>';

            try {
                // Coletar dados do formulário atual (apenas 3 campos manuais)
                const frequenciaTeste = parseFloat(document.getElementById('frequencia-teste')?.value || '');
                const capacitancia = parseFloat(document.getElementById('capacitancia')?.value || '');
                const tipoTransformadorInput = document.getElementById('tipo-transformador-induced')?.value || '';

                // Validar inputs obrigatórios do formulário (apenas 3 campos manuais)
                console.log('[induced_voltage] Validação:', {
                    frequenciaTeste,
                    capacitancia,
                    tipoTransformadorInput,
                    isFreqNaN: isNaN(frequenciaTeste),
                    isCapNaN: isNaN(capacitancia),
                    isTipoEmpty: !tipoTransformadorInput
                });

                if (isNaN(frequenciaTeste) || isNaN(capacitancia) || !tipoTransformadorInput) {
                    const missingFields = [];
                    if (!tipoTransformadorInput) missingFields.push("Tipo de ensaio");
                    if (isNaN(frequenciaTeste)) missingFields.push("Frequência de Teste");
                    if (isNaN(capacitancia)) missingFields.push("Capacitância");
                    throw new Error(`Por favor, preencha: ${missingFields.join(", ")}.`);
                }

                // Obter dados básicos do transformador
                const transformerStore = window.apiDataSystem.getStore('transformerInputs');
                const transformerData = await transformerStore.getData();
                const formData = transformerData.formData || {};

                // Obter dados de perdas (obrigatório para tipo de aço, indução, peso, perdas)
                const lossesStore = window.apiDataSystem.getStore('losses');
                const lossesData = await lossesStore.getData();
                const lossesFormData = lossesData.formData || {};

                // Obter tipo de aço das perdas (NOME CORRETO)
                const tipoAco = lossesFormData['tipo-aco'] || 'H110-27';

                // Extrair dados básicos do transformador
                const tensaoAt = parseFloat(formData.tensao_at || '0');
                const tensaoBt = parseFloat(formData.tensao_bt || '0');
                const tensaoProva = parseFloat(formData.teste_tensao_induzida_at || '0');

                // Dados do terciário (se disponíveis) - para uso quando BT excede limites SUT
                const tertiaryData = {};
                if (formData.tensao_terciario && parseFloat(formData.tensao_terciario) > 0) {
                    tertiaryData.tensao_terciario_kv = parseFloat(formData.tensao_terciario);
                }
                if (formData.corrente_nominal_terciario && parseFloat(formData.corrente_nominal_terciario) > 0) {
                    tertiaryData.corrente_nominal_terciario = parseFloat(formData.corrente_nominal_terciario);
                }

                // Validar dados básicos essenciais
                if (tensaoAt <= 0 || tensaoBt <= 0 || tensaoProva <= 0) {
                    throw new Error("Dados básicos incompletos. Verifique Tensão AT, BT e Tensão de Ensaio Induzida em Dados Básicos.");
                }

                // Preparar dados para enviar ao backend (campos manuais + terciário)
                const calculationData = {
                    freq_teste: frequenciaTeste,
                    capacitancia: capacitancia,
                    tipo_transformador: tipoTransformadorInput,
                    tipo_aco: tipoAco, // Obtido das perdas
                    // Dados de perdas e básicos serão obtidos automaticamente pelo backend
                    losses_data: lossesData,
                    // Incluir dados do terciário se disponíveis para uso quando BT excede limites SUT
                    ...tertiaryData
                };

                console.log('📤 [induced_voltage] Enviando dados para cálculo no backend:', calculationData);

                // Preparar dados no formato esperado pelo backend
                const requestData = {
                    basicData: {}, // Dados básicos serão obtidos automaticamente pelo backend
                    moduleData: calculationData
                };

                // Enviar dados para o backend para cálculo
                const response = await fetch('/api/transformer/modules/inducedVoltage/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData),
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Erro HTTP ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                console.log('📥 [induced_voltage] Resposta do backend recebida:', result);

                if (result.success && result.results) {
                    // Exibir resultados calculados pelo backend
                    displayInducedVoltageResults(result.results);

                    // Salvar resultados no store
                    await saveCalculationResults(result.results);

                    console.log('✅ [induced_voltage] Cálculos exibidos com sucesso!');
                } else {
                    throw new Error(result.message || 'Erro desconhecido no cálculo');
                }

            } catch (error) {
                console.error('❌ [induced_voltage] Erro no cálculo:', error);
                errorDiv.textContent = `Erro: ${error.message}`;
                resultsDiv.innerHTML = '';
            }

        });
    }
}

// Função para exibir os resultados calculados pelo backend
async function displayInducedVoltageResults(data) {
    console.log('🎯 [displayInducedVoltageResults] Dados recebidos:', data);

    // Separar resultados principais dos EPS
    await displayMainCalculationResults(data);
    
    // Exibir análise do EPS se disponível
    if (data.eps_analysis) {
        displayEpsAnalysisInTab(data.eps_analysis);
    } else {
        // Limpar tab do EPS se não houver dados
        const epsContainer = document.getElementById('eps-analysis-results');
        if (epsContainer) {
            epsContainer.innerHTML = '<div class="alert alert-info tab-info-alert">Nenhuma análise de EPS disponível para os dados calculados.</div>';
        }
    }

    // Automaticamente gerar análise de frequências após cálculo principal
    console.log('🔄 [induced_voltage] Gerando análise de frequências automaticamente...');
    setTimeout(() => {
        generateFrequencyAnalysis();
    }, 500); // Pequeno delay para garantir que o DOM foi atualizado
}

// Função para exibir apenas os resultados principais na primeira tab
async function displayMainCalculationResults(data) {
    const resultsDiv = document.getElementById('resultado-tensao-induzida');
    let resultsHtml = '';

    // Verificar se há resultados principais
    if (data.pot_ativa !== undefined && data.pot_magnetica !== undefined) {
        const tipoTransformador = data.tipo_transformador || 'Desconhecido';
        const isMonofasico = tipoTransformador === 'Monofásico';

        // Obter limites dinâmicos para highlighting baseados no tipo de transformador
        const limitsInfo = data.limits_info || {};
        const dutPowerLimit = limitsInfo.dut_power_limit_kw || 1350;
        const epsPowerLimit = limitsInfo.eps_aparente_power_limit_kva || 1500;
        const epsCurrentLimit = limitsInfo.eps_current_limit_positive_a || 2000;
        
        // Função para highlighting baseado em limites
        const getHighlightClass = (value, limit, isActive = true) => {
            if (!isActive || !value || !limit) return '';
            return value > limit ? 'table-danger' : '';
        };

        // Calcular highlights uma vez para evitar redundância
        const potAtivaHighlight = getHighlightClass(data.pot_ativa, dutPowerLimit);
        const potMagneticaHighlight = getHighlightClass(data.pot_magnetica, epsPowerLimit);
        const sourceHighlight = potAtivaHighlight || potMagneticaHighlight; // Usar para BT/Terciário

        // Estilo das tabelas (similar ao SCRATCH.py)
        const tableStyle = 'font-size: 0.8rem; margin-bottom: 0;';
        const cellStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #ecf0f1;';
        const valueStyle = 'text-align: center; padding: 0.3rem; background-color: #34495e; color: #ffffff; font-weight: bold;';
        const unitStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #bdc3c7;';

        // Determinar badge da fonte de tensão
        let sourceBadge = '';
        if (data.sut_tertiary_info) {
            const sutInfo = data.sut_tertiary_info;
            sourceBadge = sutInfo.using_tertiary ?
                '<span class="badge bg-warning text-dark ms-2">USANDO TERCIÁRIO DO DUT</span>' :
                '<span class="badge bg-success text-white ms-2">USANDO BT</span>';
        }

        resultsHtml += `
            <div class="card mb-3" style="background-color: #2c3e50;">
                <div class="card-header" style="background-color: #34495e; color: #ecf0f1;">
                    <h6 class="mb-0 text-center">Resultados do Cálculo - ${tipoTransformador}${sourceBadge}</h6>
                </div>
                <div class="card-body" style="padding: 0.5rem;">
                    <div class="row">
                        <!-- Parâmetros de Entrada -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-2" style="color: #ecf0f1;">Parâmetros de Entrada</h6>
                            <table class="table table-sm table-bordered" style="${tableStyle}">
                                <tbody>
                                    <tr>
                                        <td style="${cellStyle}">Tipo de Aço</td>
                                        <td style="${valueStyle}">${data.tipo_aco || 'H110-27'}</td>
                                        <td style="${unitStyle}">-</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Tensão AT</td>
                                        <td style="${valueStyle}">${(data.tensao_at || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>
`;
        // Mostrar tensão da fonte usada (BT ou Terciário) com highlighting
        if (data.sut_tertiary_info && data.sut_tertiary_info.using_tertiary) {
            // Usando terciário - mostrar dados do terciário com destaque
            resultsHtml += `
                                    <tr style="background-color: #e67e22;">
                                        <td style="${cellStyle}; font-weight: bold;">Tensão Terciário</td>
                                        <td style="${valueStyle}; color: white; font-weight: bold;" class="${sourceHighlight}">${(data.sut_tertiary_info.tensao_terciario_kv || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>`;
        } else {
            // Usando BT - mostrar dados do BT com highlighting
            resultsHtml += `
                                    <tr>
                                        <td style="${cellStyle}">Tensão BT</td>
                                        <td style="${valueStyle}" class="${sourceHighlight}">${(data.tensao_bt || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>`;
        }

        resultsHtml += `
                                    <tr>
                                        <td style="${cellStyle}">Frequência Nominal</td>
                                        <td style="${valueStyle}" class="${potMagneticaHighlight}">${(data.freq_nominal || 60).toFixed(1)}</td>
                                        <td style="${unitStyle}">Hz</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Tensão de Ensaio</td>
                                        <td style="${valueStyle}">${(data.tensao_induzida || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Indução Nominal</td>
                                        <td style="${valueStyle}">${(data.inducao_nominal || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">T</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Peso do Núcleo</td>
                                        <td style="${valueStyle}">${(data.peso_nucleo || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">Ton</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Perdas em Vazio</td>
                                        <td style="${valueStyle}">${(data.perdas_vazio || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kW</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Parâmetros do Ensaio -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-2" style="color: #ecf0f1;">Parâmetros do Ensaio</h6>
                            <table class="table table-sm table-bordered" style="${tableStyle}">
                                <tbody>
                                    <tr>
                                        <td style="${cellStyle}">Frequência de Teste</td>
                                        <td style="${valueStyle}">${(data.frequencia_teste || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">Hz</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Relação fp/fn</td>
                                        <td style="${valueStyle}">${((data.frequencia_teste || 0) / (data.freq_nominal || 60)).toFixed(2)}</td>
                                        <td style="${unitStyle}"></td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Tensão de Ensaio</td>
                                        <td style="${valueStyle}">${(data.tensao_induzida || 0).toFixed(1)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Capacitância AT-GND</td>
                                        <td style="${valueStyle}">${(data.capacitancia || 0).toFixed(0)}</td>
                                        <td style="${unitStyle}">pF</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Indução no Teste (β)</td>
                                        <td style="${valueStyle}">${(data.inducao_teste || 0).toFixed(3)}</td>
                                        <td style="${unitStyle}">T</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Fator de Potência Magnética</td>
                                        <td style="${valueStyle}">${(data.fator_potencia_mag || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">VAr/kg</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Fator de Perdas</td>
                                        <td style="${valueStyle}">${(data.fator_perdas || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">W/kg</td>
                                    </tr>`;
        // Adicionar informação sobre fonte de tensão (BT ou Terciário) com highlighting
        if (data.sut_tertiary_info) {
            const sutInfo = data.sut_tertiary_info;
            const sourceColor = sutInfo.using_tertiary ? '#e74c3c' : '#27ae60'; // Vermelho para terciário, verde para BT
            resultsHtml += `
                                    <tr style="background-color: ${sourceColor};">
                                        <td style="${cellStyle}; font-weight: bold;">Fonte de Tensão</td>
                                        <td style="${valueStyle}; color: white; font-weight: bold;" class="${sourceHighlight}">${sutInfo.voltage_source}</td>
                                        <td style="${unitStyle}">-</td>
                                    </tr>`;
        }

        resultsHtml += `
                                </tbody>
                            </table>
                        </div>

                        <!-- Resultados Calculados -->
                        <div class="col-md-4">
                            <h6 class="text-center mb-2" style="color: #ecf0f1;">Resultados (${tipoTransformador})</h6>
                            <table class="table table-sm table-bordered" style="${tableStyle}">
                                <tbody>
                                    <tr>
                                        <td style="${cellStyle}">Tensão Aplicada BT</td>
                                        <td style="${valueStyle}">${(data.tensao_aplicada_bt || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Potência Ativa Pw</td>
                                        <td style="${valueStyle}" class="${getHighlightClass(data.pot_ativa, dutPowerLimit)}">${(data.pot_ativa || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kW</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Potência ${isMonofasico ? 'Reativa Magnética' : 'Magnética'} Sm</td>
                                        <td style="${valueStyle}" class="${getHighlightClass(data.pot_magnetica, epsPowerLimit)}">${(data.pot_magnetica || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kVA</td>
                                    </tr>`;

        if (isMonofasico) {
            resultsHtml += `
                                    <tr>
                                        <td style="${cellStyle}">Corrente de Excitação Iexc</td>
                                        <td style="${valueStyle}">${(data.corrente_excitacao || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">A</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">Componente Indutiva Sind</td>
                                        <td style="${valueStyle}">${(data.pot_induzida || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kVAr ind</td>
                                    </tr>
                                    <tr>
                                        <td style="${cellStyle}">U para cálculo de Scap</td>
                                        <td style="${valueStyle}">${(data.u_dif || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kV</td>
                                    </tr>
                                    <tr style="background-color: #8B0000;">
                                        <td style="${cellStyle}; font-weight: bold;">Potência Capacitiva Scap</td>
                                        <td style="${valueStyle}; color: #ff6b6b; font-weight: bold;">${(data.pcap || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kVAr cap</td>
                                    </tr>`;
        } else {
            resultsHtml += `
                                    <tr>
                                        <td style="${cellStyle}">Corrente de Excitação Iexc</td>
                                        <td style="${valueStyle}">${(data.corrente_excitacao || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">A</td>
                                    </tr>
                                    <tr style="background-color: #2E8B57;">
                                        <td style="${cellStyle}; font-weight: bold;">Potência de Teste (Total)</td>
                                        <td style="${valueStyle}; color: #90EE90; font-weight: bold;">${(data.potencia_teste || 0).toFixed(2)}</td>
                                        <td style="${unitStyle}">kVA</td>
                                    </tr>`;
        }
        resultsHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Adicionar observações sobre SUT/Terciário se disponível
        let sutTertiaryHtml = ''; // Initialize empty string for SUT/Tertiary info
        if (data.sut_tertiary_info) {
            const sutInfo = data.sut_tertiary_info;
            const alertClass = sutInfo.using_tertiary ? 'alert-warning' : 'alert-success';
            const icon = sutInfo.using_tertiary ? '⚠️' : '✅';

            sutTertiaryHtml = `
                <div class="card mb-3">
                    <div class="card-header" style="background-color: #34495e; color: #ecf0f1;">
                        <h6 class="mb-0 text-center">Informações SUT/Terciário</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert ${alertClass} mb-0">
                            <strong>${icon} ${sutInfo.voltage_source}:</strong> ${sutInfo.status}
                        </div>
                    </div>
                </div>
            `;
        }
        
        const sutTertiaryContainer = document.getElementById('sut-terciario-info-container');
        if (sutTertiaryContainer) {
            sutTertiaryContainer.innerHTML = sutTertiaryHtml;
        } else if (sutTertiaryHtml) {
            const resultsContentDiv = document.getElementById('tab-calculation-content'); 
            if(resultsContentDiv) resultsContentDiv.insertAdjacentHTML('beforeend', sutTertiaryHtml);
        }
        
        // Move Legenda de Destaques to its new placeholder (com conteúdo tabelado)
        if (data.limits_info) {
            const limits = data.limits_info;
            const dynamicInfo = getDynamicEpsTypeInfoInduced(limits);

            // Obter dados do transformador para exibir informações adicionais
            let transformerData = {};
            try {
                const transformerStore = window.apiDataSystem.getStore('transformerInputs');
                const storeData = await transformerStore.getData();
                transformerData = storeData.formData || {};
            } catch (error) {
                console.warn('[induced_voltage] Não foi possível obter dados do transformador:', error);
            }
            const legendaHtml = `
                <div class="card mb-2">
                    <div class="card-header" style="background-color: #34495e; color: #ecf0f1;">
                        <h6 class="mb-0 text-center small">Legenda de Destaques e Informações SUT/Terciário</h6>
                    </div>
                    <div class="card-body p-2">
                        <!-- Destaques de Potência em linha -->
                        <div class="mb-2">
                            <strong class="small d-block mb-1">Destaques de Potência:</strong>
                            <div class="d-flex flex-wrap gap-1 small">
                                <span class="badge bg-danger">🔴 DUT > ${limits.dut_power_limit_kw}kW</span>
                                <span class="badge bg-danger">🔴 EPS > ${limits.eps_aparente_power_limit_kva}kVA</span>
                            </div>
                            <div class="small text-muted mt-1">
                                <em>Limites baseados no grupo de ligação</em>
                            </div>
                        </div>
                        
                        <!-- Seleção de Enrolamento DUT em linha -->
                        <div class="mb-2">
                            <strong class="small d-block mb-1">Enrolamento DUT (aplicação):</strong>
                            <div class="d-flex flex-wrap gap-1 small">
                                <span class="badge bg-success">✅ BT: Padrão (${(limits.sut_at_min_voltage_kv || 0).toFixed(1)}-${(limits.sut_at_max_voltage_kv || 140).toFixed(1)}kV)</span>
                                <span class="badge bg-warning text-dark">⚠️ TERCIÁRIO: Se excede fonte EPS/SUT</span>
                                <span class="badge bg-info text-dark">🎯 TERCIÁRIO: Otimização (delta baixo)</span>
                            </div>
                        </div>

                        <!-- Detecção EPS/SUT em linha -->
                        <div>
                            <strong class="small d-block mb-1">Tipo EPS/SUT:</strong>
                            <div class="d-flex flex-wrap gap-1 small">
                                <span class="badge bg-info text-dark">📋 Teste: ${data.tipo_transformador || 'N/A'}</span>
                                <span class="badge bg-secondary">🔧 Real: ${transformerData.tipo_transformador || 'N/A'}</span>
                                <span class="badge bg-primary">⚙️ EPS/SUT: ${dynamicInfo.transformerType}</span>
                            </div>
                            <div class="small text-muted mt-1">
                                <em>Grupo: ${transformerData.grupo_ligacao || 'N/A'}</em>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            `;
            const legendaContainer = document.getElementById('legenda-destaques-container');
            if (legendaContainer) {
                legendaContainer.innerHTML = legendaHtml;
            } else {
                const resultsDiv = document.getElementById('resultado-tensao-induzida');
                if(resultsDiv) resultsDiv.insertAdjacentHTML('beforeend', legendaHtml);
            }
        } else {
            const legendaContainer = document.getElementById('legenda-destaques-container');
            if (legendaContainer) {
                legendaContainer.innerHTML = '';
            }
        }
    } else {
        resultsHtml = `
            <div class="alert alert-warning">
                <h5>⚠️ Nenhum resultado disponível</h5>
                <p>Os cálculos foram processados, mas não há resultados para exibir. Verifique os dados de entrada.</p>
                <small>Dados recebidos: <code>${JSON.stringify(data, null, 2)}</code></small>
            </div>
        `;
    }

    document.getElementById('resultado-tensao-induzida').innerHTML = resultsHtml;
}

// Função separada para exibir análise do EPS na tab correspondente
function displayEpsAnalysisInTab(epsAnalysis) {
    const epsContainer = document.getElementById('eps-analysis-results');
    if (!epsContainer) return;

    const statusColor = epsAnalysis.status === "OK" ? "success" :
                       epsAnalysis.status === "VIOLATION" ? "danger" : "warning";
    const tableStyle = 'font-size: 0.8rem; margin-bottom: 0;';
    const cellStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #ecf0f1;';
    const valueStyle = 'text-align: center; padding: 0.3rem; background-color: #34495e; color: #ffffff; font-weight: bold;';
    const unitStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #bdc3c7;';

    let epsHtml = `
        <!-- Resumo da Análise -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="alert alert-${statusColor} mb-2">
                    <strong>Status: ${epsAnalysis.status}</strong><br>
                    <strong>Resumo:</strong> ${epsAnalysis.summary || 'Análise concluída'}
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Limites do Equipamento -->
            <div class="col-md-4">
                <h6 class="text-center mb-2" style="color: #ecf0f1;">Limites do Equipamento</h6>
                <table class="table table-sm table-bordered" style="${tableStyle}">
                    <tbody>
                        <tr>
                            <td style="${cellStyle}">Corrente EPS</td>
                            <td style="${valueStyle}">${epsAnalysis.limits_info?.eps_current_limit_positive_a || 2000}</td>
                            <td style="${unitStyle}">A</td>
                        </tr>
                        <tr>
                            <td style="${cellStyle}">Potência EPS</td>
                            <td style="${valueStyle}">${epsAnalysis.limits_info?.eps_aparente_power_limit_kva || 1500}</td>
                            <td style="${unitStyle}">kVA</td>
                        </tr>
                        <tr>
                            <td style="${cellStyle}">Potência DUT</td>
                            <td style="${valueStyle}">${epsAnalysis.limits_info?.dut_power_limit_kw || 1350}</td>
                            <td style="${unitStyle}">kW</td>
                        </tr>
                    </tbody>
                </table>
            </div>`;

    // Violações
    if (epsAnalysis.violations && epsAnalysis.violations.length > 0) {
        epsHtml += `
            <div class="col-md-4">
                <h6 class="text-center mb-2 text-danger">⚠️ Violações Detectadas</h6>
                <div style="max-height: 200px; overflow-y: auto;">`;
        epsAnalysis.violations.forEach(violation => {
            epsHtml += `
                    <div class="alert alert-danger mb-2 py-2">
                        <strong>${violation.type}:</strong><br>
                        ${violation.description}
                        ${violation.percent_over ? `<br><small>Excesso: +${violation.percent_over.toFixed(1)}%</small>` : ''}
                    </div>`;
        });
        epsHtml += `</div></div>`;
    }

    // Avisos
    if (epsAnalysis.warnings && epsAnalysis.warnings.length > 0) {
        epsHtml += `
            <div class="col-md-4">
                <h6 class="text-center mb-2 text-warning">⚡ Avisos</h6>
                <div style="max-height: 200px; overflow-y: auto;">`;
        epsAnalysis.warnings.forEach(warning => {
            epsHtml += `
                    <div class="alert alert-warning mb-2 py-2">
                        <strong>${warning.type}:</strong><br>
                        ${warning.description}
                        ${warning.percent_usage ? `<br><small>Uso: ${warning.percent_usage.toFixed(1)}%</small>` : ''}
                    </div>`;
        });
        epsHtml += `</div></div>`;
    }
    // Análise do SUT se disponível
    if (epsAnalysis.sut_analysis && epsAnalysis.sut_analysis.recommended_taps) {
        epsHtml += `
            <div class="col-md-4">
                <h6 class="text-center mb-2" style="color: #ecf0f1;">🔧 Taps SUT Recomendados</h6>
                <table class="table table-sm table-bordered" style="${tableStyle}">
                    <thead>
                        <tr>
                            <th style="${cellStyle}">Tap (kV)</th>
                            <th style="${cellStyle}">I_EPS (A)</th>
                            <th style="${cellStyle}">% Limite</th>
                            <th style="${cellStyle}">Status</th>
                        </tr>
                    </thead>
                    <tbody>`;

        epsAnalysis.sut_analysis.recommended_taps.forEach(tap => {
            const statusClass = tap.color_class === 'success' ? 'text-success' :
                               tap.color_class === 'warning' ? 'text-warning' :
                               tap.color_class === 'danger' ? 'text-danger' : 'text-danger';

            epsHtml += `
                        <tr>
                            <td style="${valueStyle}">${tap.sut_tap_kv}</td>
                            <td style="${valueStyle}">${tap.corrente_eps_a}</td>
                            <td style="${valueStyle}">${tap.percent_limite_eps}%</td>
                            <td style="${valueStyle}" class="${statusClass}">${tap.status}</td>
                        </tr>`;
        });

        epsHtml += `</tbody></table></div>`;
    }
    epsHtml += `</div>`;

    epsContainer.innerHTML = epsHtml;
}

// Função para gerar tabela de análise de frequências
async function generateFrequencyAnalysis() {
    try {
        console.log('[induced_voltage] ===== INICIANDO GERAÇÃO DE TABELA DE FREQUÊNCIAS =====');

        // Obter dados do último cálculo
        console.log('[induced_voltage] Obtendo dados do store...');
        const store = window.apiDataSystem.getStore('inducedVoltage');
        const storeData = await store.getData();
        console.log('[induced_voltage] Dados do store:', storeData);

        if (!storeData.calculationResults) {
            console.error('[induced_voltage] Nenhum resultado de cálculo encontrado no store para análise de frequência');
            throw new Error('Execute o cálculo principal primeiro para obter os resultados.');
        }

        // Usar os resultados do último cálculo para a análise de frequência
        const requestData = storeData.calculationResults;

        console.log('[induced_voltage] Enviando dados para análise de frequências:', requestData);

        // Chamar endpoint do backend
        const response = await fetch('/api/transformer/modules/inducedVoltage/frequency-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || 'Erro na geração da tabela');
        }

        const data = await response.json();
        console.log('[induced_voltage] Tabela de frequências recebida:', data);

        // Exibir tabela
        displayFrequencyAnalysisTable(data.frequency_analysis);

    } catch (error) {
        console.error('[induced_voltage] Erro ao gerar tabela de frequências:', error);

        const container = document.getElementById('frequency-table-container');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Erro:</strong> ${error.message}
                </div>
            `;
        }
    }
}

// Função para obter informações dinâmicas do tipo EPS/SUT baseado nos limites (similar ao losses.js)
function getDynamicEpsTypeInfoInduced(limites) {
    const epsApparentPowerLimitKva = limites?.eps_aparente_power_limit_kva || 1450.0;
    const epsCurrentLimitPosA = limites?.eps_current_limit_positive_a || 2000.0;
    const dutPowerLimitKw = limites?.dut_power_limit_kw || 1350.0;

    // Usar o tipo determinado pelo backend se disponível, caso contrário usar padrão
    let tipoFonteEPS = limites?.sut_eps_type || "Trifásico";
    let capacidadeEPS = `${epsApparentPowerLimitKva} kVA`;
    let limiteDUT = `${dutPowerLimitKw} kW`;
    let limiteCorrenteEPS = `±${epsCurrentLimitPosA} A`;

    return {
        tipoFonteEPS,
        capacidadeEPS,
        epsCurrentLimitPosA,
        dutPowerLimitKw,
        limiteDUT,
        limiteCorrenteEPS,
        transformerType: tipoFonteEPS  // Manter compatibilidade
    };
}

// Função para exibir a tabela de análise de frequências
function displayFrequencyAnalysisTable(frequencyData) {
    const container = document.getElementById('frequency-table-container');
    if (!container) return;

    const tipoTransformador = frequencyData.tipo_transformador;
    const tableData = frequencyData.table_data;

    if (!tableData || tableData.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">Nenhum dado de frequência disponível</div>';
        return;
    }
    // Estilos padronizados (igual à tabela de referência)
    const tableStyle = 'font-size: 0.8rem; margin-bottom: 0;';
    const cellStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #ecf0f1;';
    const valueStyle = 'text-align: center; padding: 0.3rem; background-color: #34495e; color: #ffffff; font-weight: bold;';
    const unitStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #bdc3c7;';
    const headerStyle = 'text-align: center; padding: 0.3rem; background-color: #2c3e50; color: #ecf0f1; font-weight: bold;';

    let tableHtml = `
        <div class="card mb-3" style="background-color: #2c3e50; border: 1px solid #34495e;">
            <div class="card-header" style="background-color: #34495e; color: #ecf0f1; border-bottom: 2px solid #2c3e50;">
                <h5 class="mb-1 text-center" style="font-weight: bold;">
                    📊 Análise de Frequências - ${tipoTransformador}
                </h5>
                <p class="mb-0 text-center" style="font-size: 0.9rem; color: #bdc3c7;">
                    Resultados calculados para frequências: 100, 120, 150, 180, 200, 240 Hz
                </p>
            </div>
            <div class="card-body" style="padding: 1rem; background-color: #2c3e50;">
                <div class="row">
                    <!-- Tabela à esquerda (reduzida em 30%) -->
                    <div class="col-md-5">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered" style="${tableStyle}">
                                <thead>
                                    <tr>
    `;
    // Cabeçalho da tabela baseado no tipo
    if (tipoTransformador === 'Monofásico') {
        tableHtml += `
                                <th style="${headerStyle}">Frequência<br>(Hz)</th>
                                <th style="${headerStyle}">Potência Ativa<br>Pw (kW)</th>
                                <th style="${headerStyle}">Potência Reativa<br>Magnética Sm (kVA)</th>
                                <th style="${headerStyle}">Componente<br>Indutiva Sind (kVAr ind)</th>
                                <th style="${headerStyle}">Potência Capacitiva<br>Scap (kVAr cap)</th>
                                <th style="${headerStyle}">Relação<br>Scap/Sind</th>
        `;
    } else { // Trifásico
        tableHtml += `
                                <th style="${headerStyle}">Frequência<br>(Hz)</th>
                                <th style="${headerStyle}">Potência Ativa<br>Pw (kW)</th>
                                <th style="${headerStyle}">Potência Reativa<br>Magnética Sm (kVA)</th>
                                <th style="${headerStyle}">Potência Capacitiva<br>Scap (kVAr cap)</th>
        `;
    }

    tableHtml += `
                        </tr>
                    </thead>
                    <tbody>
    `;

    // Linhas da tabela
    tableData.forEach((row, index) => {
        const rowBg = index % 2 === 0 ? '#2c3e50' : '#34495e';
        const freqStyle = `text-align: center; padding: 0.4rem; background-color: ${rowBg}; color: #ecf0f1; font-weight: bold; border: 1px solid #34495e;`;
        const dataStyle = `text-align: center; padding: 0.4rem; background-color: ${rowBg}; color: #ffffff; border: 1px solid #34495e;`;
        const capacitiveStyle = `text-align: center; padding: 0.4rem; background-color: ${rowBg}; color: #ff6b6b; font-weight: bold; border: 1px solid #34495e;`;
        const ratioStyle = `text-align: center; padding: 0.4rem; background-color: ${rowBg}; color: #90EE90; font-weight: bold; border: 1px solid #34495e;`;

        tableHtml += '<tr>';
        tableHtml += `<td style="${freqStyle}">${row.frequencia}</td>`;
        tableHtml += `<td style="${dataStyle}">${row.pot_ativa.toFixed(2)}</td>`;
        tableHtml += `<td style="${dataStyle}">${row.pot_magnetica.toFixed(2)}</td>`;

        if (tipoTransformador === 'Monofásico') {
            tableHtml += `<td style="${dataStyle}">${row.pot_induzida.toFixed(2)}</td>`;
            tableHtml += `<td style="${capacitiveStyle}">${Math.abs(row.pcap).toFixed(2)}</td>`;
            tableHtml += `<td style="${ratioStyle}">${row.scap_sind_ratio.toFixed(3)}</td>`;
        } else {
            tableHtml += `<td style="${capacitiveStyle}">${Math.abs(row.pcap).toFixed(2)}</td>`;
        }

        tableHtml += '</tr>';
    });

    tableHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Gráfico único à direita (aumentado para ocupar mais espaço) -->
                <div class="col-md-7">
                    <div class="card" style="background-color: #34495e; border: 1px solid #2c3e50;">
                        <div class="card-header" style="background-color: #2c3e50; color: #ecf0f1; padding: 0.5rem;">
                            <h6 class="mb-2 text-center" style="font-weight: bold;">
                                📈 Análise Gráfica vs Frequência
                            </h6>
                            <!-- Seletor de dados para visualização -->
                            <div class="d-flex justify-content-center">
                                <select id="chart-data-selector" class="form-select form-select-sm"
                                        style="width: auto; background-color: #34495e; color: #ecf0f1; border: 1px solid #2c3e50; font-size: 0.8rem;">
                                    <option value="pot_ativa">Potência Ativa (kW)</option>
                                    <option value="pot_magnetica">Potência Magnética (kVA)</option>
                                    <option value="pot_capacitiva">Potência Capacitiva (kVAr)</option>`;

    if (tipoTransformador === 'Monofásico') {
        tableHtml += `
                                    <option value="pot_induzida">Componente Indutiva (kVAr)</option>
                                    <option value="scap_sind_ratio">Relação Scap/Sind</option>`;
    }

    tableHtml += `
                                    <option value="comparativo">Comparativo Geral</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body" style="padding: 0.5rem;">
                            <canvas id="frequency-analysis-chart" width="350" height="300"
                                    style="background-color: #34495e; border-radius: 5px;"></canvas>
                        </div>
                        <div class="card-footer" style="background-color: #2c3e50; color: #bdc3c7; padding: 0.3rem; font-size: 0.7rem; text-align: center;">
                            <small>
                                <i class="fas fa-mouse-pointer"></i>
                                Selecione o tipo de dados acima para alterar a visualização
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer" style="background-color: #34495e; color: #bdc3c7; text-align: center; font-size: 0.8rem;">
                <small>
                    <i class="fas fa-info-circle"></i>
                    Tabela e gráficos gerados automaticamente baseados nos parâmetros de entrada |
                    Valores em vermelho: Potência Capacitiva |
                    Valores em verde: Relação Scap/Sind
                </small>
            </div>
        </div>
    `;

    container.innerHTML = tableHtml;
    console.log('[induced_voltage] Tabela de análise de frequências exibida');

    // Gerar gráficos após inserir o HTML
    setTimeout(() => {
        console.log('[induced_voltage] ===== INICIANDO GRÁFICOS =====');

        // Verificar se Chart.js está disponível
        if (typeof Chart === 'undefined') {
            console.error('[induced_voltage] Chart.js NÃO ESTÁ CARREGADO!');
            showChartError();
            return;
        }

        const canvas = document.getElementById('frequency-analysis-chart');
        if (canvas) {
            console.log('[induced_voltage] Chart.js disponível, gerando gráfico...');
            generateFrequencyCharts(frequencyData);
        } else {
            console.error('[induced_voltage] Canvas não encontrado após timeout!');
        }
    }, 500); // Aumentado um pouco o delay para renderização
}


// Variável global para armazenar o gráfico e dados
let frequencyChart = null;
let chartData = null;

// Função para gerar gráfico único de frequência
function generateFrequencyCharts(frequencyData) {
    console.log('[induced_voltage] ===== GERANDO GRÁFICO =====');

    // Verificar se Chart.js está carregado
    if (typeof Chart === 'undefined') {
        console.error('[induced_voltage] Chart.js não está carregado');
        return;
    }

    const tableData = frequencyData.table_data;
    const tipoTransformador = frequencyData.tipo_transformador;

    if (!tableData || tableData.length === 0) {
        console.warn('[induced_voltage] Nenhum dado disponível para gráficos');
        return;
    }

    const frequencies = tableData.map(row => row.frequencia);
    const potAtiva = tableData.map(row => row.pot_ativa);

    const canvas = document.getElementById('frequency-analysis-chart');
    if (!canvas) {
        console.error('[induced_voltage] Canvas não encontrado!');
        return;
    }
    
    // Destruir gráfico existente
    if (frequencyChart) {
        frequencyChart.destroy();
    }

    try {
        frequencyChart = new Chart(canvas, {
            type: 'line',
            data: {
                labels: frequencies,
                datasets: [{
                    label: 'Potência Ativa (kW)',
                    data: potAtiva,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.2)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#ecf0f1',
                            boxWidth: 12,
                            padding: 15
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Frequência (Hz)',
                            color: '#ecf0f1'
                        },
                        ticks: { color: '#ecf0f1' },
                        grid: { color: 'rgba(236, 240, 241, 0.1)' }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Potência (kW)',
                            color: '#ecf0f1'
                        },
                        ticks: { color: '#ecf0f1' },
                        grid: { color: 'rgba(236, 240, 241, 0.1)' }
                    }
                }
            }
        });

        console.log('[induced_voltage] ✅ GRÁFICO CRIADO COM SUCESSO!');

        chartData = {
            frequencies: frequencies,
            potAtiva: potAtiva,
            potMagnetica: tableData.map(row => row.pot_magnetica),
            potCapacitiva: tableData.map(row => Math.abs(row.pcap || 0)),
            potInduzida: tableData.map(row => row.pot_induzida || 0),
            scapSindRatio: tableData.map(row => row.scap_sind_ratio || 0),
            tipoTransformador: tipoTransformador
        };
        
        setupChartSelector();

    } catch (error) {
        console.error('[induced_voltage] ❌ ERRO AO CRIAR GRÁFICO:', error);
    }
}

// Função para configurar o seletor do gráfico
function setupChartSelector() {
    const selector = document.getElementById('chart-data-selector');
    if (selector) {
        selector.removeEventListener('change', handleChartSelectorChange);
        selector.addEventListener('change', handleChartSelectorChange);
        // Set initial value to 'pot_ativa' to match the initial chart
        selector.value = 'pot_ativa';
        console.log('[induced_voltage] Seletor configurado');
    }
}

// Handler para mudança no seletor
function handleChartSelectorChange(event) {
    const dataType = event.target.value;
    console.log('[induced_voltage] Mudando gráfico para:', dataType);
    updateChart(dataType);
}

// Função para atualizar gráfico
function updateChart(dataType) {
    if (!frequencyChart || !chartData) {
        console.error('[induced_voltage] Gráfico ou dados não disponíveis');
        return;
    }

    if (dataType === 'comparativo') {
        updateComparativeChart();
        return;
    }

    let newData, label, color, bgColor, yAxisTitle;
    
    // Clear potential second y-axis
    delete frequencyChart.options.scales.y1;

    switch (dataType) {
        case 'pot_ativa':
            newData = chartData.potAtiva;
            label = 'Potência Ativa (kW)';
            color = '#3498db';
            bgColor = 'rgba(52, 152, 219, 0.2)';
            yAxisTitle = 'Potência (kW)';
            break;
        case 'pot_magnetica':
            newData = chartData.potMagnetica;
            label = 'Potência Magnética (kVA)';
            color = '#e74c3c';
            bgColor = 'rgba(231, 76, 60, 0.2)';
            yAxisTitle = 'Potência (kVA)';
            break;
        case 'pot_capacitiva':
            newData = chartData.potCapacitiva;
            label = 'Potência Capacitiva (kVAr)';
            color = '#f39c12';
            bgColor = 'rgba(243, 156, 18, 0.2)';
            yAxisTitle = 'Potência (kVAr)';
            break;
        case 'pot_induzida':
            newData = chartData.potInduzida;
            label = 'Componente Indutiva (kVAr)';
            color = '#9b59b6';
            bgColor = 'rgba(155, 89, 182, 0.2)';
            yAxisTitle = 'Potência (kVAr)';
            break;
        case 'scap_sind_ratio':
            newData = chartData.scapSindRatio;
            label = 'Relação Scap/Sind';
            color = '#2ecc71';
            bgColor = 'rgba(46, 204, 113, 0.2)';
            yAxisTitle = 'Relação (Adimensional)';
            break;
        default:
            return;
    }

    frequencyChart.data.datasets = [{
        data: newData,
        label: label,
        borderColor: color,
        backgroundColor: bgColor,
        borderWidth: 2,
        fill: true,
        tension: 0.4
    }];
    frequencyChart.options.scales.y.title.text = yAxisTitle;
    frequencyChart.update();
    console.log('[induced_voltage] Gráfico atualizado para:', label);
}

// Função para atualizar gráfico comparativo
function updateComparativeChart() {
    if (!frequencyChart || !chartData) return;

    const datasets = [
        {
            label: 'Pot. Ativa (kW)', data: chartData.potAtiva, borderColor: '#3498db',
            backgroundColor: 'transparent', yAxisID: 'y'
        },
        {
            label: 'Pot. Magnética (kVA)', data: chartData.potMagnetica, borderColor: '#e74c3c',
            backgroundColor: 'transparent', yAxisID: 'y'
        },
        {
            label: 'Pot. Capacitiva (kVAr)', data: chartData.potCapacitiva, borderColor: '#f39c12',
            backgroundColor: 'transparent', yAxisID: 'y'
        }
    ];

    frequencyChart.options.scales.y.title.text = 'Potência (kW/kVA/kVAr)';
    delete frequencyChart.options.scales.y1;

    if (chartData.tipoTransformador === 'Monofásico') {
        datasets.push({
            label: 'Comp. Indutiva (kVAr)', data: chartData.potInduzida, borderColor: '#9b59b6',
            backgroundColor: 'transparent', yAxisID: 'y'
        });
        datasets.push({
            label: 'Relação Scap/Sind', data: chartData.scapSindRatio, borderColor: '#2ecc71',
            backgroundColor: 'transparent', yAxisID: 'y1'
        });

        frequencyChart.options.scales.y1 = {
            type: 'linear', display: true, position: 'right',
            title: { display: true, text: 'Relação', color: '#ecf0f1' },
            ticks: { color: '#ecf0f1' },
            grid: { drawOnChartArea: false }
        };
    }

    // Assign new datasets
    frequencyChart.data.datasets = datasets.map(ds => ({
        ...ds, borderWidth: 2, fill: false, tension: 0.4
    }));

    frequencyChart.update();
    console.log('[induced_voltage] Gráfico comparativo atualizado');
}

// Função para mostrar erro quando Chart.js não carrega
function showChartError() {
    const canvas = document.getElementById('frequency-analysis-chart');
    if (canvas) {
        const container = canvas.parentElement;
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center" style="margin: 20px;">
                    <h6><i class="fas fa-exclamation-triangle"></i> Erro ao Carregar Gráfico</h6>
                    <p class="mb-0">Não foi possível carregar a biblioteca Chart.js.</p>
                </div>
            `;
        }
    }
}

// Função para limpar tabela de frequências
function clearFrequencyAnalysisTable() {
    const container = document.getElementById('frequency-table-container');
    if (container) {
        if (frequencyChart) {
            frequencyChart.destroy();
            frequencyChart = null;
        }
        chartData = null;
        container.innerHTML = '';
        console.log('[induced_voltage] Tabela de frequências e gráfico limpos');
    }
}

// Configurar botões da tabela de frequências
async function setupFrequencyTableButtons() {
    const clearFreqTableBtn = document.getElementById('clear-frequency-table-btn');
    if (clearFreqTableBtn) {
        clearFreqTableBtn.addEventListener('click', clearFrequencyAnalysisTable);
    }
}

// Função para limpar valores padrão indesejados
function clearUnwantedDefaultValues() {
    // Frequência 120 Hz agora é considerada um valor válido e será persistida
    const capInput = document.getElementById('capacitancia');
    if (capInput && capInput.value === '1500') {
        capInput.value = '';
    }
}

// Função de inicialização do módulo Tensão Induzida
async function initInducedVoltage() {
    console.log('Módulo Tensão Induzida carregado.');

    if (window.setupApiFormPersistence) {
        await window.setupApiFormPersistence('induced-voltage-form', 'inducedVoltage');
    }
    
    await loadInducedVoltageDataAndPopulateForm();

    document.addEventListener('transformerDataUpdated', async (event) => {
        console.log('[induced_voltage] Evento transformerDataUpdated recebido:', event.detail);
        await loadInducedVoltageDataAndPopulateForm();
        await fillInputsFromTransformerData();
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);
    });

    const transformerInfoPlaceholderId = 'transformer-info-induced_voltage-page';
    const placeholderElement = document.getElementById(transformerInfoPlaceholderId);
    if (placeholderElement) {
        await loadAndPopulateTransformerInfo(transformerInfoPlaceholderId);
    }

    await fillInputsFromTransformerData();
    clearUnwantedDefaultValues();

    const tipoSelect = document.getElementById('tipo-transformador-induced');
    if (tipoSelect && !tipoSelect.value) {
        tipoSelect.value = 'Trifásico';
    }

    await setupCalcButton();
    await setupFrequencyTableButtons();
}

// SPA routing e fallback de carregamento
document.addEventListener('moduleContentLoaded', (event) => {
    if (event.detail && event.detail.moduleName === 'induced_voltage') {
        initInducedVoltage();
    }
});
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('induced-voltage-form')) {
        initInducedVoltage();
    }
});